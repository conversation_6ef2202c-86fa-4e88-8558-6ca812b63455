(function () {
    const containers = document.querySelectorAll('[data-category-source="api"]');
    if (!containers.length) {
        return;
    }

    const endpoint = '/api/categories.php?status=active';

    fetch(endpoint, { credentials: 'include' })
        .then(function (response) {
            if (!response.ok) {
                throw new Error('Failed to load categories');
            }
            return response.json();
        })
        .then(function (data) {
            if (!Array.isArray(data)) {
                throw new Error('Unexpected response');
            }
            containers.forEach(function (container) {
                renderCategoryList(container, data);
            });
        })
        .catch(function () {
            containers.forEach(function (container) {
                showCategoryError(container);
            });
        });

    function renderCategoryList(container, categories) {
        const emptyMessage = container.getAttribute('data-empty-message') || 'No categories available right now.';
        container.innerHTML = '';

        if (!categories.length) {
            const empty = document.createElement('p');
            empty.className = 'empty-category-message';
            empty.textContent = emptyMessage;
            container.appendChild(empty);
            return;
        }

        const prefix = container.getAttribute('data-category-prefix') || 'cat';

        categories.forEach(function (category, index) {
            const wrapper = document.createElement('div');
            wrapper.className = 'single-category';

            const input = document.createElement('input');
            input.type = 'checkbox';
            const rawSlug = ((category && category.slug) || '').toString();
            const rawName = ((category && category.name) || '').toString();
            const base = rawSlug !== '' ? rawSlug : rawName.toLowerCase();
            const sanitized = base.replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '') || String(index + 1);
            const id = prefix + '-' + sanitized + '-' + index;
            input.id = id;

            const label = document.createElement('label');
            label.setAttribute('for', id);
            label.textContent = rawName || 'Category';

            wrapper.appendChild(input);
            wrapper.appendChild(label);

            container.appendChild(wrapper);
        });
    }

    function showCategoryError(container) {
        container.innerHTML = '';
        const error = document.createElement('p');
        error.className = 'empty-category-message';
        error.textContent = container.getAttribute('data-error-message') || 'Unable to load categories.';
        container.appendChild(error);
    }
})();
