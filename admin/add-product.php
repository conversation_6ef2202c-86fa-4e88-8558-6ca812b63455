<?php
require __DIR__ . '/init.php';

$productForm = [
    'name' => '',
    'regular_price' => '',
    'sale_price' => '',
    'stock' => '',
    'sku' => '',
    'category' => '',
    'tags' => '',
    'description' => '',
    'status' => 'published',
    'image_url' => '',
];
$productErrors = [];
$productSuccess = rcf_flash('product_success');
$categories = array_values(array_filter(rcf_categories(), static function (array $category): bool {
    return ($category['status'] ?? 'active') !== 'archived';
}));

if (rcf_is_post()) {
    $productForm = array_merge($productForm, [
        'name' => trim((string)($_POST['name'] ?? '')),
        'regular_price' => trim((string)($_POST['regular_price'] ?? '')),
        'sale_price' => trim((string)($_POST['sale_price'] ?? '')),
        'stock' => trim((string)($_POST['stock'] ?? '')),
        'sku' => trim((string)($_POST['sku'] ?? '')),
        'category' => trim((string)($_POST['category'] ?? '')),
        'tags' => trim((string)($_POST['tags'] ?? '')),
        'description' => trim((string)($_POST['description'] ?? '')),
        'status' => $_POST['status'] ?? 'published',
        'image_url' => trim((string)($_POST['image_url'] ?? '')),
    ]);

    $token = $_POST['csrf_token'] ?? '';
    if (!rcf_verify_csrf($token)) {
        $productErrors['general'] = 'Your session timed out. Please try again.';
    } else {
        if ($productForm['name'] === '') {
            $productErrors['name'] = 'Product name is required.';
        }

        if ($productForm['regular_price'] === '' && $productForm['sale_price'] === '') {
            $productErrors['regular_price'] = 'Provide a price or sale price.';
        }

        if ($productForm['sku'] === '') {
            $productErrors['sku'] = 'SKU is required.';
        }

        if ($productForm['stock'] === '' || !is_numeric($productForm['stock'])) {
            $productErrors['stock'] = 'Stock must be a number.';
        }

        $status = $productForm['status'] === 'draft' ? 'draft' : 'published';

        if (empty($productErrors)) {
            $price = $productForm['sale_price'] !== '' ? $productForm['sale_price'] : $productForm['regular_price'];
            $price = (float)str_replace([',', '$'], ['', ''], $price);
            $stock = (int)$productForm['stock'];
            $imagePath = $productForm['image_url'];

            if (!empty($_FILES['product_image']['name'])) {
                $upload = $_FILES['product_image'];
                    if ($upload['error'] === UPLOAD_ERR_OK) {
                        $extension = strtolower(pathinfo($upload['name'], PATHINFO_EXTENSION));
                        try {
                            $suffix = bin2hex(random_bytes(4));
                        } catch (Exception $e) {
                            $suffix = (string)mt_rand(1000, 9999);
                        }
                        $fileName = 'product_' . time() . '_' . $suffix . ($extension ? '.' . $extension : '');
                        $targetDir = rcf_base_path('assets/images/products');
                        if (!is_dir($targetDir)) {
                            mkdir($targetDir, 0775, true);
                        }
                        $targetPath = $targetDir . '/' . $fileName;
                        if (move_uploaded_file($upload['tmp_name'], $targetPath)) {
                            $imagePath = 'assets/images/products/' . $fileName;
                        }
                    }
                }

            if ($imagePath === '') {
                $imagePath = 'assets/images/store/01.jpg';
            }

            $products = rcf_products();
            $products[] = [
                'id' => rcf_next_id($products),
                'name' => $productForm['name'],
                'sku' => $productForm['sku'],
                'price' => $price,
                'stock' => $stock,
                'status' => $status,
                'category' => $productForm['category'],
                'image' => $imagePath,
                'description' => $productForm['description'],
                'tags' => $productForm['tags'],
                'created_at' => date(DATE_ATOM),
            ];

            rcf_save_products($products);
            rcf_flash('product_success', 'Product created successfully.');
            rcf_redirect('/admin/product-list.php');
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
    <!-- rts header area start -->
    <head>
        <meta charset="UTF-8">
        <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="Grocery, Store, stores">
        <title>RC Furnishing Admin</title>
        <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
        <!-- plugins css -->
        <link rel="stylesheet" href="assets/css/plugins.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css">
        <link rel="stylesheet" href="assets/css/style.css">
</head>
    <!-- rts header area end -->
<body>
<?php include __DIR__ . '/includes/admin-header.php'; ?>

            <div class="body-root-inner">
                <div class="transection">
                    <div class="vendor-list-main-wrapper product-wrapper add-product-page">
                        <div class="card-body table-product-select">
                            <div class=" header-two show right-collups-add-product">
                                <!-- inner menu area desktop start -->
                                <div class="right-collups-area-top">
                                    <h6 class="title" style="font-size: 32px;">Add New Product</h6>
                                    <p>Add information and add new product</p>
                                </div>
                                <form action="" method="post" enctype="multipart/form-data" class="input-main-wrapper">
                                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(rcf_csrf_token(), ENT_QUOTES); ?>">
                                    <?php if (!empty($productErrors['general'])): ?>
                                        <div class="auth-feedback error"><?php echo htmlspecialchars($productErrors['general'], ENT_QUOTES); ?></div>
                                    <?php endif; ?>
                                    <div class="single-input">
                                        <label for="product-name">Product Name</label>
                                        <input type="text" id="product-name" name="name" value="<?php echo htmlspecialchars($productForm['name'], ENT_QUOTES); ?>" placeholder="Modular Sofa Set" required>
                                        <?php if (!empty($productErrors['name'])): ?>
                                            <span class="form-field-error"><?php echo htmlspecialchars($productErrors['name'], ENT_QUOTES); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="single-input">
                                        <label for="regular-price">Regular Price</label>
                                        <input type="text" id="regular-price" name="regular_price" value="<?php echo htmlspecialchars($productForm['regular_price'], ENT_QUOTES); ?>" placeholder="2199">
                                        <?php if (!empty($productErrors['regular_price'])): ?>
                                            <span class="form-field-error"><?php echo htmlspecialchars($productErrors['regular_price'], ENT_QUOTES); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="single-input">
                                        <label for="sale-price">Sale Price</label>
                                        <input type="text" id="sale-price" name="sale_price" value="<?php echo htmlspecialchars($productForm['sale_price'], ENT_QUOTES); ?>" placeholder="1999">
                                    </div>
                                    <div class="single-input">
                                        <label for="stock">Stock</label>
                                        <input type="number" id="stock" name="stock" value="<?php echo htmlspecialchars($productForm['stock'], ENT_QUOTES); ?>" placeholder="50" required>
                                        <?php if (!empty($productErrors['stock'])): ?>
                                            <span class="form-field-error"><?php echo htmlspecialchars($productErrors['stock'], ENT_QUOTES); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="single-input">
                                        <label for="sku">SKU</label>
                                        <input type="text" id="sku" name="sku" value="<?php echo htmlspecialchars($productForm['sku'], ENT_QUOTES); ?>" placeholder="RCF-005" required>
                                        <?php if (!empty($productErrors['sku'])): ?>
                                            <span class="form-field-error"><?php echo htmlspecialchars($productErrors['sku'], ENT_QUOTES); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="single-input">
                                        <label for="category">Category</label>
                                        <input type="text" id="category" name="category" list="category-options" value="<?php echo htmlspecialchars($productForm['category'], ENT_QUOTES); ?>" placeholder="Living Room">
                                        <datalist id="category-options">
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars((string)$category['name'], ENT_QUOTES); ?>"></option>
                                            <?php endforeach; ?>
                                        </datalist>
                                    </div>
                                    <div class="single-input">
                                        <label for="tags">Tags</label>
                                        <input type="text" id="tags" name="tags" value="<?php echo htmlspecialchars($productForm['tags'], ENT_QUOTES); ?>" placeholder="sofa, fabric, modular">
                                    </div>
                                    <div class="single-input">
                                        <label for="status">Status</label>
                                        <select id="status" name="status">
                                            <option value="published" <?php echo $productForm['status'] === 'published' ? 'selected' : ''; ?>>Published</option>
                                            <option value="draft" <?php echo $productForm['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                        </select>
                                    </div>
                                    <div class="single-input">
                                        <label for="description">Description</label>
                                        <textarea name="description" id="description" placeholder="Type something" rows="4"><?php echo htmlspecialchars($productForm['description'], ENT_QUOTES); ?></textarea>
                                    </div>
                                    <div class="single-input">
                                        <label for="image-url">Image URL</label>
                                        <input type="text" id="image-url" name="image_url" value="<?php echo htmlspecialchars($productForm['image_url'], ENT_QUOTES); ?>" placeholder="assets/images/store/02.jpg">
                                    </div>
                                    <div class="single-input">
                                        <label for="product-image">Upload Image</label>
                                        <div class="file-upload-add-product">
                                            <div class="profile-left">
                                                <div class="profile-image mb--30">
                                                    <img id="rts_image" src="<?php echo htmlspecialchars($productForm['image_url'] ?: 'assets/images/grocery/16.png', ENT_QUOTES); ?>" alt="Product preview">
                                                    <span>Drag and drop or choose a file</span>
                                                </div>
                                                <div class="button-area">
                                                    <div class="brows-file-wrapper">
                                                        <input name="product_image" id="product-image" type="file" accept="image/*">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-area-botton-wrapper-p-list">
                                        <button class="rts-btn btn-primary" type="submit">Save Product</button>
                                        <a href="/admin/product-list.php" class="rts-btn btn-primary bg-transparent">Cancel</a>
                                    </div>
                                </form>
                            </div>
                            
                        </div>
                     </div>
                </div>
                <!-- bottom footer areas start -->
                <div class="footer-copyright">
                    <div class="left">
                        <p>Copyright © 2024 All Right Reserved.</p>
                    </div>
                    <ul>
                        <li><a href="#">Terms</a></li>
                        <li><a href="#">Privacy</a></li>
                        <li><a href="#">Help</a></li>
                    </ul>
                    </div>
                <!-- bottom footer areas end -->
            </div>

        </div>

    </div>



  <!-- rts header area start -->
  


<!-- progress area start -->
<div class="progress-wrap active-progress">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
    </svg>
</div>
<!-- progress area end -->




<div class="search-input-area">
    <div class="container">
        <div class="search-input-inner">
            <div class="input-div">
                <input class="search-input autocomplete ui-autocomplete-input" type="text" placeholder="Search by keyword or #" autocomplete="off">
                <button><i class="far fa-search"></i></button>
            </div>
        </div>
    </div>
    <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
</div>


<div id="anywhere-home"></div>

<div id="side-bar" class="side-bar header-two right-collups-add-product">
    <button class="close-icon-menu"><i class="far fa-times"></i></button>
    <!-- inner menu area desktop start -->
    <div class="right-collups-area-top">
        <h6 class="title">Add New Product</h6>
        <p>Add information and add new product</p>
    </div>
    <div class="input-main-wrapper">
        <div class="single-input">
            <label for="one">Product Name</label>
            <input type="text" id="one" placeholder="Quaker Oats Healthy Meal...">
        </div>
        <div class="single-input">
            <label for="Two">Regular Price</label>
            <input type="text" id="Two" placeholder="240">
        </div>
        <div class="single-input">
            <label for="sale">Sale Price</label>
            <input type="text" id="sale" placeholder="$250">
        </div>
        <div class="single-input">
            <label for="Stock">Stock</label>
            <input type="text" id="Stock" placeholder="530">
        </div>
        <div class="single-input">
            <label for="sku">SKU</label>
            <input type="text" id="sku" placeholder="3245">
        </div>
        <div class="single-input">
            <label for="cate">Category</label>
            <input type="text" id="cate" placeholder="Notebook">
        </div>
        <div class="single-input">
            <label for="Tag">Tag</label>
            <input type="text" id="Tag" placeholder="Iphone, Mobile">
        </div>
        <div class="single-input">
            <label for="text">Description</label>
            <textarea name="text" id="text" placeholder="Type something"></textarea>
        </div>
        <div class="single-input">
            <div class="file-upload-add-product">
                <div class="profile-left">
                    <div class="profile-image mb--30">
                        <img id="rts_image" src="assets/images/grocery/16.png" alt="Profile-NFT">
                        <span>Drag and drop Image</span>
                    </div>
                    <div class="button-area">
                        <div class="brows-file-wrapper">
                            <!-- actual upload which is hidden -->
                            <input name="rts_images1" id="rts_images1" type="file">
                            <!-- our custom upload button -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-area-botton-wrapper-p-list">
            <button class="rts-btn btn-primary">Save</button>
            <button class="rts-btn btn-primary bg-transparent">Cancel</button>
        </div>
    </div>
</div>
<?php include __DIR__ . '/includes/admin-footer.php'; ?>
