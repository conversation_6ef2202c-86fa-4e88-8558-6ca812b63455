<?php
require __DIR__ . '/init.php';

$categoryForm = [
    'id' => null,
    'name' => '',
    'description' => '',
    'status' => 'active',
];
$categoryFormErrors = [];
$bulkErrors = [];

$successMessage = rcf_flash('category_success');
$errorMessage = rcf_flash('category_error');

$editId = isset($_GET['edit']) ? (int)$_GET['edit'] : null;

if (rcf_is_post()) {
    $action = $_POST['form_action'] ?? '';
    $token = $_POST['csrf_token'] ?? '';

    if (!rcf_verify_csrf($token)) {
        $categoryFormErrors['general'] = 'Your session timed out. Please try again.';
    } else {
        switch ($action) {
            case 'create':
                $categoryForm = [
                    'id' => null,
                    'name' => trim((string)($_POST['name'] ?? '')),
                    'description' => trim((string)($_POST['description'] ?? '')),
                    'status' => $_POST['status'] ?? 'active',
                ];
                $created = rcf_create_category($categoryForm, $categoryFormErrors);
                if ($created !== null) {
                    rcf_flash('category_success', sprintf('Category "%s" created successfully.', $created['name']));
                    rcf_redirect('/admin/categories.php');
                }
                break;

            case 'update':
                $editId = (int)($_POST['id'] ?? 0);
                $categoryForm = [
                    'id' => $editId,
                    'name' => trim((string)($_POST['name'] ?? '')),
                    'description' => trim((string)($_POST['description'] ?? '')),
                    'status' => $_POST['status'] ?? 'active',
                ];

                if ($editId <= 0) {
                    $categoryFormErrors['general'] = 'A valid category is required.';
                    break;
                }

                $productsAdjusted = 0;
                $updated = rcf_update_category($editId, $categoryForm, $categoryFormErrors, $productsAdjusted);
                if ($updated !== null) {
                    $message = sprintf('Category "%s" updated successfully.', $updated['name']);
                    if ($productsAdjusted > 0) {
                        $message .= sprintf(' %d product%s updated.', $productsAdjusted, $productsAdjusted === 1 ? '' : 's');
                    }
                    rcf_flash('category_success', $message);
                    rcf_redirect('/admin/categories.php');
                }
                break;

            case 'delete':
                $deleteId = (int)($_POST['id'] ?? 0);
                if ($deleteId <= 0) {
                    rcf_flash('category_error', 'Unable to delete the requested category.');
                    rcf_redirect('/admin/categories.php');
                }
                $productsAdjusted = 0;
                if (!rcf_delete_category($deleteId, $productsAdjusted)) {
                    rcf_flash('category_error', 'Category not found or already removed.');
                } else {
                    $message = 'Category deleted successfully.';
                    if ($productsAdjusted > 0) {
                        $message .= sprintf(' %d product%s moved out of the category.', $productsAdjusted, $productsAdjusted === 1 ? '' : 's');
                    }
                    rcf_flash('category_success', $message);
                }
                rcf_redirect('/admin/categories.php');
                break;

            case 'upload':
                $upload = $_FILES['categories_file'] ?? null;
                if (!$upload || ($upload['error'] ?? UPLOAD_ERR_NO_FILE) === UPLOAD_ERR_NO_FILE) {
                    $bulkErrors[] = 'Choose a CSV or JSON file to upload.';
                    break;
                }

                if ($upload['error'] !== UPLOAD_ERR_OK) {
                    $bulkErrors[] = 'Upload failed. Please try again.';
                    break;
                }

                $extension = strtolower(pathinfo((string)$upload['name'], PATHINFO_EXTENSION));
                if (!in_array($extension, ['csv', 'json'], true)) {
                    $bulkErrors[] = 'Unsupported file format. Use CSV or JSON.';
                    break;
                }

                $parseErrors = [];
                $rows = rcf_parse_category_upload($upload['tmp_name'], $extension, $parseErrors);
                if (!empty($parseErrors)) {
                    $bulkErrors = array_merge($bulkErrors, $parseErrors);
                    break;
                }

                $summary = rcf_import_categories($rows);
                $message = sprintf(
                    'Bulk import complete. %d new, %d updated. %d product%s adjusted.',
                    (int)($summary['created'] ?? 0),
                    (int)($summary['updated'] ?? 0),
                    (int)($summary['products_adjusted'] ?? 0),
                    (int)($summary['products_adjusted'] ?? 0) === 1 ? '' : 's'
                );
                if (!empty($summary['errors'])) {
                    rcf_flash('category_error', implode(' ', $summary['errors']));
                }
                rcf_flash('category_success', $message);
                rcf_redirect('/admin/categories.php');
                break;

            default:
                $categoryFormErrors['general'] = 'Unknown action requested.';
        }
    }
}

$categories = rcf_categories();

if ($editId && empty($categoryFormErrors) && !rcf_is_post()) {
    $category = rcf_category($editId);
    if ($category) {
        $categoryForm = [
            'id' => (int)$category['id'],
            'name' => (string)$category['name'],
            'description' => (string)($category['description'] ?? ''),
            'status' => (string)($category['status'] ?? 'active'),
        ];
    } else {
        rcf_flash('category_error', 'The requested category could not be found.');
        rcf_redirect('/admin/categories.php');
    }
}

$products = rcf_products();
$productCounts = [];
foreach ($products as $product) {
    $categoryName = trim((string)($product['category'] ?? ''));
    if ($categoryName === '') {
        continue;
    }
    $key = strtolower($categoryName);
    $productCounts[$key] = ($productCounts[$key] ?? 0) + 1;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet" href="assets/css/plugins.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="p-d-page">
<?php include __DIR__ . '/includes/admin-header.php'; ?>

            <div class="body-root-inner">
                <div class="transection">
                    <div class="title-right-actioin-btn-wrapper-product-list">
                        <h3 class="title">Categories</h3>
                        <div class="button-wrapper">
                            <div class="single-select">
                                <div class="nice-select" tabindex="0"><span class="current"><?php echo count($categories); ?> Total</span>
                                    <ul class="list">
                                        <li data-value="Default Sorting" data-display="<?php echo count($categories); ?> Total" class="option selected">Active Categories</li>
                                    </ul>
                                </div>
                            </div>
                            <a class="rts-btn btn-primary" href="#category-form">Add Category</a>
                        </div>
                    </div>

                    <?php if (!empty($successMessage)): ?>
                        <div class="auth-feedback success" style="margin-bottom: 20px;">
                            <?php echo htmlspecialchars($successMessage, ENT_QUOTES); ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($errorMessage)): ?>
                        <div class="auth-feedback error" style="margin-bottom: 20px;">
                            <?php echo htmlspecialchars($errorMessage, ENT_QUOTES); ?>
                        </div>
                    <?php endif; ?>

                    <div class="row g-5">
                        <div class="col-xl-8 col-lg-12">
                            <div class="vendor-list-main-wrapper product-wrapper">
                                <div class="card-body table-product-select">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="category-table" cellspacing="0" width="100%">
                                            <thead class="mb--20">
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Products</th>
                                                    <th>Updated</th>
                                                    <th class="text-end">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <?php if (empty($categories)): ?>
                                                <tr>
                                                    <td colspan="5">No categories yet. Add your first category to organize the catalog.</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($categories as $categoryItem): ?>
                                                    <?php
                                                    $status = strtolower((string)($categoryItem['status'] ?? 'active'));
                                                    $statusLabel = ucfirst($status);
                                                    $productsInCategory = $productCounts[strtolower((string)$categoryItem['name'])] ?? 0;
                                                    $updated = isset($categoryItem['updated_at']) ? date('M j, Y', strtotime((string)$categoryItem['updated_at'])) : '—';
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <div class="item-check-area-table-left">
                                                                <div class="item-image-and-name">
                                                                    <div class="category-meta">
                                                                        <p class="mb-0"><?php echo htmlspecialchars($categoryItem['name'], ENT_QUOTES); ?></p>
                                                                        <span class="sku">Slug: <?php echo htmlspecialchars($categoryItem['slug'], ENT_QUOTES); ?></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="status-badge" style="text-transform: capitalize;">
                                                                <?php echo htmlspecialchars($statusLabel, ENT_QUOTES); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <p><?php echo (int)$productsInCategory; ?></p>
                                                        </td>
                                                        <td>
                                                            <p><?php echo htmlspecialchars($updated, ENT_QUOTES); ?></p>
                                                        </td>
                                                        <td class="text-end">
                                                            <div class="d-flex justify-content-end" style="gap: 12px;">
                                                                <a class="rts-btn btn-white" href="/admin/categories.php?edit=<?php echo (int)$categoryItem['id']; ?>#category-form">Edit</a>
                                                                <form method="post" style="display:inline;" onsubmit="return confirm('Delete this category? Products assigned will lose their category.');">
                                                                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(rcf_csrf_token(), ENT_QUOTES); ?>">
                                                                    <input type="hidden" name="form_action" value="delete">
                                                                    <input type="hidden" name="id" value="<?php echo (int)$categoryItem['id']; ?>">
                                                                    <button type="submit" class="rts-btn btn-danger">Delete</button>
                                                                </form>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-12">
                            <div class="single-add-product-wrapper" id="category-form">
                                <div class="right-collups-area-top">
                                    <h6 class="title"><?php echo $categoryForm['id'] ? 'Edit Category' : 'Add New Category'; ?></h6>
                                    <p>Manage catalog categories and sync them with products.</p>
                                </div>
                                <?php if (!empty($categoryFormErrors['general'])): ?>
                                    <div class="auth-feedback error" style="margin-bottom:16px;">
                                        <?php echo htmlspecialchars($categoryFormErrors['general'], ENT_QUOTES); ?>
                                    </div>
                                <?php endif; ?>
                                <form class="input-main-wrapper" method="post">
                                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(rcf_csrf_token(), ENT_QUOTES); ?>">
                                    <input type="hidden" name="form_action" value="<?php echo $categoryForm['id'] ? 'update' : 'create'; ?>">
                                    <?php if ($categoryForm['id']): ?>
                                        <input type="hidden" name="id" value="<?php echo (int)$categoryForm['id']; ?>">
                                    <?php endif; ?>
                                    <div class="single-input">
                                        <label for="category-name">Name</label>
                                        <input type="text" id="category-name" name="name" value="<?php echo htmlspecialchars($categoryForm['name'], ENT_QUOTES); ?>" placeholder="Living Room" required>
                                        <?php if (!empty($categoryFormErrors['name'])): ?>
                                            <span class="form-field-error"><?php echo htmlspecialchars($categoryFormErrors['name'], ENT_QUOTES); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="single-input">
                                        <label for="category-status">Status</label>
                                        <select id="category-status" name="status">
                                            <option value="active" <?php echo $categoryForm['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="hidden" <?php echo $categoryForm['status'] === 'hidden' ? 'selected' : ''; ?>>Hidden</option>
                                            <option value="archived" <?php echo $categoryForm['status'] === 'archived' ? 'selected' : ''; ?>>Archived</option>
                                        </select>
                                    </div>
                                    <div class="single-input">
                                        <label for="category-description">Description</label>
                                        <textarea id="category-description" name="description" rows="3" placeholder="Short summary for the storefront."><?php echo htmlspecialchars($categoryForm['description'], ENT_QUOTES); ?></textarea>
                                    </div>
                                    <div class="button-wrapper mt--20">
                                        <button class="rts-btn btn-primary" type="submit"><?php echo $categoryForm['id'] ? 'Save Changes' : 'Create Category'; ?></button>
                                        <?php if ($categoryForm['id']): ?>
                                            <a class="rts-btn btn-white" href="/admin/categories.php">Cancel</a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>

                            <div class="single-add-product-wrapper" style="margin-top: 30px;">
                                <div class="right-collups-area-top">
                                    <h6 class="title">Bulk Import</h6>
                                    <p>Upload a CSV or JSON file to create or update categories in bulk.</p>
                                </div>
                                <?php if (!empty($bulkErrors)): ?>
                                    <div class="auth-feedback error" style="margin-bottom:16px;">
                                        <?php echo htmlspecialchars(implode(' ', $bulkErrors), ENT_QUOTES); ?>
                                    </div>
                                <?php endif; ?>
                                <form class="input-main-wrapper" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(rcf_csrf_token(), ENT_QUOTES); ?>">
                                    <input type="hidden" name="form_action" value="upload">
                                    <div class="single-input">
                                        <label for="categories-file">Select File</label>
                                        <input type="file" id="categories-file" name="categories_file" accept=".csv,.json" required>
                                        <span class="input__note">Columns supported: name, description, status, slug.</span>
                                    </div>
                                    <div class="button-wrapper mt--20">
                                        <button class="rts-btn btn-primary" type="submit">Upload Categories</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer-copyright">
                    <div class="left">
                        <p>Copyright © 2024 All Right Reserved.</p>
                    </div>
                    <ul>
                        <li><a href="#">Terms</a></li>
                        <li><a href="#">Privacy</a></li>
                        <li><a href="#">Help</a></li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

<div class="progress-wrap active-progress">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
    </svg>
</div>

<div class="search-input-area">
    <div class="container">
        <div class="search-input-inner">
            <div class="input-div">
                <input class="search-input autocomplete ui-autocomplete-input" type="text" placeholder="Search by keyword or #" autocomplete="off">
                <button><i class="far fa-search"></i></button>
            </div>
        </div>
    </div>
    <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
</div>

<div id="anywhere-home"></div>

<script src="assets/js/plugins.js"></script>
<script src="assets/js/main.js"></script>
<script>
    (function(){
        if (window.jQuery && window.jQuery.fn.DataTable) {
            jQuery('#category-table').DataTable({
                pagingType: 'numbers',
                pageLength: 10,
                ordering: true,
                order: [[0, 'asc']]
            });
        }
    })();
</script>
</body>
</html>
