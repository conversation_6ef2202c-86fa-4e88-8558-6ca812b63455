{"version": 3, "sources": ["style.scss", "default/_typography.scss", "default/_variables.scss", "default/_spacing.scss", "default/_reset.scss", "default/_forms.scss", "default/_shortcode.scss", "default/_mixins.scss", "default/_animations.scss", "default/_text-animation.scss", "default/_sal.scss", "elements/common.scss", "elements/_left-sidebar.scss", "elements/_profile-setting.scss", "elements/_vendor.scss", "elements/_button.scss", "elements/_nice-select.scss", "elements/_shopping-card.scss", "elements/_apexchart.scss", "elements/_cart-sidebar.scss", "elements/_search-input.scss", "elements/_backto-top.scss", "elements/_registration.scss", "elements/_brand.scss", "elements/_order-details.scss"], "names": [], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAAA;AA+CA;AC9CQ;AACA;ACFR;EAGI;EACA;EAGA;EAGA;EAEA;EAGA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EAKA;EAIA;EACA;EACA;EAGA;EAEA;EACA;EAIA;EAEA;EACA;EAIA;EACA;EACA;EACA;EACA;EACA;;;AD9EJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EAXJ;IAYQ;;;AAEJ;EAdJ;IAeQ;;;AAEJ;EAjBJ;IAkBQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EAEA;;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAqBI;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYI;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;;AAMJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYI;;AAGJ;EACI;;AAIJ;EACI;;;AAIR;EAEI;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;;AAKR;EAEI;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;EAGJ;AAAA;IAEI;;;AAOR;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;;;AAIJ;AAAA;AAAA;AAAA;EAII;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;IAQQ;IACA;IACA;;;AAEJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;EACA;;AAGJ;EACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;AAAA;AAAA;AAAA;EAII;;;AAGJ;EACI;;;AAGJ;AAAA;AAAA;AAGA;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;;AAIQ;EACI;EACA;EACA;EACA;EACA;;AAEA;EAPJ;IAQQ;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACI;;AAMhB;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAEA;EACI;;AAKZ;EACI;;;AAIR;EACI;;AAEA;EACI;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAEA;EACI;;AAKZ;EACI;;;AAOJ;EACI;;;AAIJ;EACI;EACA;;;AAKJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;IACA;;;AAEJ;EAXJ;IAYQ;IACA;;;AAEJ;EAfJ;IAgBQ;IACA;;;AAEJ;EAnBJ;IAoBQ;IACA;;;;AAIJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;IACA;;;AAEJ;EAXJ;IAYQ;IACA;;;AAEJ;EAfJ;IAgBQ;IACA;;;AAEJ;EAnBJ;IAoBQ;IACA;;;;AAIJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;IACA;;;AAEJ;EAXJ;IAYQ;IACA;;;AAEJ;EAfJ;IAgBQ;IACA;;;AAEJ;EAnBJ;IAoBQ;IACA;;;;AE3hBR;AAAA;AAAA;AAKA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAIR;EACI;;AAEA;EAHJ;IAIQ;;;AAGJ;EAPJ;IAQQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAIR;EACI;;AAEA;EAHJ;IAIQ;;;AAGJ;EAPJ;IAQQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAOR;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAThB;EAAkB;;;AAClB;EAAkB;;;AAClB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAiB;;;AACjB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAChB;EAAgB;;;AAGpB;EAEQ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAKZ;EAEQ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAjCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAMZ;EAEI;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAKA;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAOZ;EACI;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAIA;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EArCJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAIR;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAKR;EAEQ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EA7BJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;;;AAOR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AANR;EACI;EACA;;AAEA;EACI;EACA;;;AAKZ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAIA;EADJ;IAEQ;;;;AAKJ;EADJ;IAEQ;;;;AAIJ;EADJ;IAEQ;;;;AAMJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;;AAKJ;EADJ;IAEQ;;;;AAKJ;EADJ;IAEQ;;;;AAKJ;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;;AAMJ;EADJ;IAEQ;;;;AAIR;EACI;;;AAKA;EADJ;IAEQ;IACA;;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;IACA;;;;AAIR;EACI;EACA;;;AAEJ;EACI;EACA;;;ACnkBJ;EACI;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUI;;;AAIJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;;;AAIJ;AAAA;AAAA;EAGI;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;;;AAIJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;AAAA;EAGI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;;;AAIA;EACI;;;AAIR;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;;;AAGJ;AAAA;EAEI;;;AAGJ;AAAA;EAEI;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;EAGI;;;AAGJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;AAAA;EAGI;;;AAIJ;AACA;AAAA;EAEI;EACA;EACA;EACA;EACA;;;AAGJ;AAAA;AAAA;AAAA;EAII;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;;;AClWJ;AACA;AAAA;AAAA;AAAA;EAII;EACA;EACA;EACA;EACA;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEI;EACA;;;AAIR;AAAA;AAAA;AAAA;EAII;;;AAGJ;EACI;EACA;;;AAkBJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAEA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AAEA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AAEA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;AACA;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACI;EACA;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;AAIR;AAAA;AAAA;AAAA;AAAA;AAAA;EACI;;;AAsBR;AAAA;EAEI;EACA;EACA;EACA;EACA;;AACA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;AAAA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAOA;AAAA;EACI;;;AAOZ;EACI;;;AAOA;EACI;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAKZ;EACI;;AAEA;EACI;EACA;EACA;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;;;AAKZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;EACA;;;ACnSR;AAAA;AAAA;AAKA;AAAA;EAEI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;AACA;EACI;;;AAIR;EACI;;;AAGJ;AAAA;AAAA;ACxBQ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;ADoCZ;AAAA;AAAA;AAGA;EACI;EACA;EACA;;;AAIA;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AAKR;AACA;EACI;EACA;;;AAGJ;AAAA;AAAA;AAGA;EACI;EACA;;AAEA;EACI;EACA;;;AAIR;EACI;EACA;;AAEA;EACI;EACA;;;AAIR;EACI;EACA;;AAEA;EACI;EACA;;;AAIR;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAKZ;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAMZ;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAMZ;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAMZ;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAKZ;EACI;EACA;;AAGA;EALJ;IAMQ;IACA;;;AAGJ;EAVJ;IAWQ;IACA;;;AAGJ;EAfJ;IAgBQ;IACA;;;AAGJ;EApBJ;IAqBQ;IACA;;;AAGJ;EAEI;EACA;;AAGA;EANJ;IAOQ;IACA;;;AAGJ;EAXJ;IAYQ;IACA;;;AAGJ;EAhBJ;IAiBQ;IACA;;;AAGJ;EArBJ;IAsBQ;IACA;;;;AAKZ;AAAA;AAAA;AAGA;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;AAAA;AAIA;AAAA;AAAA;EAGI;EACA;;;AAGJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMI;EACA;;;AAIJ;AAAA;AAAA;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAKA;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AAHJ;AAAA;AAAA;EAGI;;;AEtfR;AAAA;AAAA;AAAA;;AAAA;AAAA;AAQA;EACI;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAGJ;EACI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;AAAA;EAEI;EACA;;;AAGJ;EACI;IAKI;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAKI;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAKJ;AACA;EACI;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAIJ;EACE;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAIJ;EACE;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAGJ;EACE;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAIJ;EACE;IACE;IACA;;EAEF;IACE;;EAEF;IACE;IACA;;;AAIN;EAEI;IAGI;;EAGJ;IAEI;;;AAIR;EAEI;IAGI;;EAGJ;IAEI;;;AAIR;EACI;EACA;;;AAIJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAEI;IACA;;EAGJ;IAKI;IACA;;EAGJ;IAII;IACA;;;AAIR;EAEI;IAEI;IACA;;EAGJ;IAKI;IACA;;EAGJ;IAII;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IAEI;IACA;;EAGJ;IAII;IACA;;EAGJ;IAGI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IAEI;IACA;;EAGJ;IAII;IACA;;EAGJ;IAGI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;AAEA;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAGI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAGI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EAEI;IAMI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EAEI;IAMI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EAEI;IAKI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IAEI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAGR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAKR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;IACI;;EAGJ;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;EACA;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;IACA;IACA;;EAGJ;IAEI;IACA;IACA;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;AAEA;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;AAEA;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;;;AAIR;EACI;IACI;;EAGJ;IACI;IACA;IACA;;EAGJ;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIR;EACI;IACI;IACA;IACA;IACA;IACA;;EAGJ;IACI;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAGR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAIR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;;;AAGR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAGR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAQR;EACI;EACA;;AAEA;EACE;;AAGF;EARJ;IASM;;EAEA;IACE;;;;AAQR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;EACA;;;AAIJ;EACI;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAIN;EACI;IACE;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;;;AAIN;EACI;IACE;;;AAIN;EACI;IACE;;;AAKN;EACI;IACI;IACA;;EAEJ;IACI;IACA;;;AAQR;EACI;IACE;;EAEF;IACI;;EAEJ;IACI;;;AAGR;EACI;IACI;;EAEF;IACI;;EAEJ;IACI;;;AAIV;EACI;IACI;;EAEF;IACI;;EAEJ;IACI;;;AAKV;EACI;;AACA;EACI;;;AAGR;EACI;;AACA;EACI;;;AAIR;EACI;IACE;IACA;IACA;IACA;;EAEF;IACE;IACA;;EAEF;IACE;IACA;IACA;IACA;;;AAQN;EACI;IACI;;EAEJ;IACI;;EAEJ;IACI;;;AAGR;EACI;IACI;IACA;;;ACpmHR;EACI;IACI;IACA;IACA;;EAGJ;IACI;IACA;IACA;;;AAIR;EACI;IACI;;EAGJ;IACI;;;AAKR;EACI;;;AAGJ;EACI;IACI;;;AAIR;EACI;IACI;;;AAIR;EACI;EACA;;;AAGJ;EACI;IACI;IACA;;;AAIR;EACI;IACI;;;AAIR;EACI;IACI;;;AAIR;EACI;IACI;;;AAIR;EACI;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;;AAAA;;AAAA;AAMA;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;AACA;EACI;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;ACvIJ;AAAA;AAAA;AAWC;AAAA;AAAA;AA0CA;AAAA;AAAA;AAIA;EACI,qBAtDqB;EAuDrB,kBAtDkB;EAuDlB;EACA;EACA;;;AAIA;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AAKJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AADJ;EACI;;;AAMJ;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAgEV;EACI,4BAjEM;;;AAqEd;AAAA;AAAA;AAKA;EACI;EACA;;;AAGJ;AAAA;EAEI;;;AAIJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAIJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;EACA;;;AAIJ;EACI;EACA;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;EACI;;;AAGJ;AAAA;EAEI;;;AV/GL;AW3DA;EACI;;;ACAJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAGA;EADJ;IAEQ;;;AAEJ;EAJJ;IAKQ;;;;AAKZ;EACI;;AACA;EACI;;;AAIR;EACI;EACA;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;IACA;;;AAEJ;EAPJ;IAQQ;IACA;;;AAGA;EADJ;IAEQ;IACA;IACA;;;AAEJ;EANJ;IAOQ;IACA;IACA;;;;AAMhB;EACI;EACA;EACA;;AAEI;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AAKhB;EACI;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACI;;;AAWxB;EACI;;;AAKJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEL;EACK;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEI;EACI;;AAEJ;EACI;;AAIJ;EACI;;AAGR;EACI;;AAEI;EACI;;AAIZ;EACI;EACA;;;AAOZ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EAbJ;IAcQ;;;AAEJ;EAhBJ;IAiBQ;IACA;IACA;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AAKZ;EACI;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EAbJ;IAcQ;IACA;;;AAEJ;EAjBJ;IAkBQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAjBJ;IAkBQ;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAYpC;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EAdJ;IAeQ;;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AAIJ;EACI;EACA;;AAMpB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;AAIJ;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EAAsB;;AACtB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKhB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;;;AAMhB;EACI;EACA;EACA;EACA;EACA;;AAEI;EACI;;AACA;EACI;EACA;EACA;EACA;;AAGA;EACI;;AAEJ;EACI;;;AAQpB;EACI;;;AAGJ;EACI;EACA;;;AAIA;EACI;EACA;;AACA;EACI;;;AAMR;EACI;EACA;;AACA;EACI;;;AAQJ;EACI;EACA;;AACA;EACI;;;AChrBhB;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EARJ;IASQ;IACA;;;AAEJ;EAZJ;IAaQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAGA;EACI;;AAGA;EACI;EACA;;;AAQxB;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;;AAOpB;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;;AAKJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;IACA;;;AAEJ;EACI;;AACA;EAFJ;IAGQ;IACA;;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAMZ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAShB;AAAA;EACI;;AACA;AAAA;EACI;;AAGR;AAAA;EACI;EACA;EACA;;AAEJ;AAAA;EACI;EACA;EACA;;;AAIR;EACI;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AAKJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AASxB;EACI;IACI;;EAKJ;IACI;IACA;;;AAKR;EACI;EACA;EACA;;;ACpYJ;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;EACA;EACA;;;AAIR;EACI;;AACA;EAFJ;IAGQ;;;;AAIR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAEJ;EAZJ;IAaQ;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAIZ;EACI;EACA;;AACA;EAHJ;IAIQ;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAIZ;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;;AAEJ;EACI;;AACA;EAFJ;IAGQ;;;AAIZ;EACI;;;AAKR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;EACA;IACI;;;AAGR;EAZJ;IAaQ;;;;AAKJ;EACI;;AACA;EACI;;AACA;EACI;EACA;EACA;;AAMJ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AAEI;EACI;EACA;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;;AAGR;EACI;;AAKhB;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;;;AAMhB;EACI;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;;;AAKZ;EACI;EACA;EACA;EACA;EACA;;AAEI;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EACI;EACA;;;AAOhB;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGR;EACI;EACA;;;AAIR;EACI;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAGA;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AAMZ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;;;AAEJ;EATJ;IAUQ;;;AAEJ;EAZJ;IAaQ;;;AAGR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;;AAMZ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAGA;EADJ;IAEQ;IACA;IACA;;;AAEJ;EANJ;IAOQ;IACA;IACA;;;AAIJ;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;EACA;;AAIZ;EACI;;AACA;EACI;;AAEJ;EACI;;AACA;EACI;EACA;EACA;;AAEI;EACI;;;AAWxB;EADJ;IAEQ;;EACA;IACI;;;;AAOR;EADJ;IAEQ;;EACA;IACI;;;AAGR;EAPJ;IAQQ;;EACA;IACI;;;;AAKR;EADJ;IAEQ;;EACA;IACI;;;AAGR;EAPJ;IAQQ;;EACA;IACI;;;;AAKR;EADJ;IAEQ;;EACA;IACI;;;AAGR;EAPJ;IAQQ;IACA;;EACA;IACI;;;AAGR;EAdJ;IAeQ;;EACA;IACI;;;AAGR;EApBJ;IAqBQ;;EACA;IACI;;;;AAQR;EACI;;;AAMJ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;;AAGR;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAGR;EACI;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EAEA;;;AAGR;EACI;;;AAOQ;EACI;;;AAMZ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAIR;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIR;EACG;;;AAEH;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMR;EACI;;;AAIJ;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAII;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAXA;EACI;;AAWJ;EACI;EACA;;;AAKhB;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAII;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAXA;EACI;;AAWJ;EACI;EACA;;;AAOhB;EACI;;;AChsBJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAGA;EACI;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AC1DhB;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACG;EACC;EACI;EACR;EACA;;;AACA;EACE;;;AACF;EACE;;;AACF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI;EACI;EACR;EACI;EACI;EACR;EACA;EACA;;;AACF;EACE;EACI;EACI;;;AACV;EACE;EACA;EACA;EACI;EACI;;;AACV;EACE;EACA;EACA;;;AACA;EACE;;;AACJ;EACE;;;AACA;EACE;EACA;;;AACJ;EACE;;;AACA;EACE;EACA;;;AACJ;EACE;EACA;EACA;;;AACA;EACE;EACA;;;AACF;EACE;EACA;;;AACJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI;EACI;EACR;EACI;EACI;EACR;EACA;EACA;;;AACA;EACE;;;AACJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AACF;EACE;;;AACF;EACE;EACA;EACA;;;AAEN;EACE;;;AAEF;EACE;;;AAMJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMJ;EACI;EACA;EACA;;AAGA;EACI;;AAEJ;EACI;EACA;;AAGA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;;AAOpB;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAIR;EACI;EACA;EACA;;AACA;EACI;;;AAIR;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;;AAKZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;IACA;IACA;;;AAEJ;EACI;EACA;EACA;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;;AAQhB;EACI;;;AC7QR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EAVJ;IAWQ;IACA;;;AAEJ;EAdJ;IAeQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACI;;;AAER;EAZJ;IAaQ;IACA;;;AAEJ;EACI;EACA;;AACC;EAHL;IAIQ;IACA;;;AAEJ;EAPJ;IAQQ;IACA;;;AAEJ;EAXJ;IAYQ;IACA;;;AAGR;EACI;;AACA;EACI;;AAIZ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;AAEJ;EACI;;;AAOR;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAMpB;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AAEJ;EACI;;AACA;EACI;EACA;;AAMpB;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;EACA;;AAPA;EACI;EACA;;AAQJ;EACI;EACA;EACA;;AAGA;EACI;;AAIZ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKhB;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AAGA;EACI;;AAIJ;EACI;EACA;;AAKhB;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;;;AAEJ;EAVJ;IAWQ;IACA;;;AAEJ;EACI;;AAEJ;EACI;EACA;;AAMJ;EACI;;AAIR;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;;AAEI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;EACI;;AAIZ;EACI;;AACA;EACI;;AAGR;EACI;;AACA;EACI;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AAGA;EACI;;;AAQpB;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;IACA;;;AAEJ;EACI;;AACA;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;;;AAMZ;EACI;;AACA;EACI;;AACA;EACI;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;AAMJ;EACI;EACA;;;AAOpB;EACI;EACA;EACA;;AAEI;EACI;EACA;;AAGR;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;;AAGR;EACI;EACA;;AAGR;EACI;;;AAIR;EACI;EACA;EACA;;;ACjfJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAcJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;;AAIJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;;AAIR;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAEJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;IACA;;;AAEJ;EARJ;IASQ;IACA;IACA;;;AAEJ;EACI;EACA;;;AAOZ;EACI;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;;AAIJ;EACI;EACA;EACA;;AACA;EAJJ;IAKQ;;;AAEJ;EAPJ;IAQQ;;;AAGR;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAOZ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAIZ;AAAA;AAAA;EAGI;EACA;;AACA;EALJ;AAAA;AAAA;IAMQ;;;AAOJ;EACI;;AAEJ;AAAA;AAAA;EAGI;EACA;;AAMR;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAKJ;EACI;EACA;EACA;EACA;EACA;;AAGA;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;;AAOZ;EACI;;;AAIR;EACI;;;AC/OJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;AACA;EACI;;AAGA;EACI;EACA;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;AACA;EACI;;;AAGR;EACI;EACA;;;AAGJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAEJ;EACI;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;;AAGR;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;;;AAMJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAlBJ;IAmBQ;;;AAGA;EACI;EACA;;AACA;EACI;EACA;;AAGR;EACI;EACA;;AACA;EACA;EACA;EACA;;AAEA;EACI;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;;;AAMpB;EACI;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKR;EACI;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAQI;EACI;EACA;EACA;;AAEJ;EACI;EACA;;AAIJ;EACI;;AACA;EACI;EACA;EACA;;AAEJ;AAAA;EAEI;EACA;EACA;;AAEJ;EACI;;;AAOZ;EACI;EACA;EACA;;AACA;EACI;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;;AAWR;EACI;EACA;EACA;EACA;EACA;EACA;;;AAQZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EAVJ;IAWQ;IACA;IACA;;;AAGA;EACI;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;;;AAMhB;EACI;;;AChiBJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;;AACA;EACI;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;;AC1GJ;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACG;;;AAGJ;EACC;EACA;EACA;EACA;;;AAED;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACG;EACA;EACA;EACH;EACA;EACG;;;AAEJ;EACC;EACG;EACA;EACA;;;AAEJ;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACG;EACA;;;AAEJ;EACC;;;AAED;EACC;;;AAED;EACI;EACH;EACA;;;AAED;EACI;EACA;EACH;EACA;EACG;EACA;;;AAMH;EACC;;AAED;EACC;EACA;EACA;;;ACrGF;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAEJ;EAVJ;IAWQ;;;AAGA;EACI;EACA;EACA;;;AAKZ;EACI;;AACA;EACI;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAIZ;EACI;EACA;;AAGA;EACI;EACA;EACA;EACA;EACA;EAKA;;AAJA;EACI;EACA;;AAGJ;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;;AACA;EAPJ;IAQQ;;;AAIZ;EACI;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AAGR;EACI;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;;;AC3GhB;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;;;AAKJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;;AAOhB;EACI;;;ACpCJ;EACI;;AACA;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;IACA;IACA;;;AAEJ;EAXJ;IAYQ;IACA;IACA;;;AAEJ;EACI;EACA;EACA;;AACA;EACI;;AAGR;EACI;EACA;EACA;EACA;EACA;;AACA;EANJ;IAOQ;;;AAEJ;EATJ;IAUQ;;;AAGA;EACI;;AAEJ;EACI;EACA;;;AAOpB;EACI;EACA;EACA;EACA;;AACA;EACI;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EACI;;AACA;EAFJ;IAGQ;;;AAEJ;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EACI;;AACA;EACI;EACA;;;AAOpB;EACI;EACA;EACA;EACA;EACA;;;AAKA;EAEI;EACA;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;AAEJ;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAEJ;EAXJ;IAYQ;;;AAGR;EACI;EACA;EACA;EACA;;AACA;EALJ;IAMQ;;;AAEJ;EARJ;IASQ;;;AAGA;EADJ;IAEQ;;;AAIJ;EACI;;AAEJ;EACI;;AAIJ;EADJ;IAEQ", "file": "../css/style.css", "sourcesContent": ["/*=================Scss Indexing=============\r\n1.variables\r\n2.typography\r\n3.spacing\r\n4.reset\r\n5.forms\r\n6.mixins\r\n7.shortcode\r\n8.animations\r\n9.text-animation\r\n10.sal\r\n11.header\r\n12.mobile-menu\r\n13.button\r\n14.nav\r\n15.banner\r\n16.swiper\r\n17.funfacts\r\n18.cta\r\n19.about\r\n20.common\r\n21.service\r\n22.projects\r\n23.working-process\r\n24.blog\r\n25.blog-details\r\n26.footer\r\n27.search-input\r\n28./side-bar\r\n29.team\r\n30.testimonials\r\n31.faq\r\n32.pricing\r\n33.date-picker\r\n34.time-picker\r\n35.appoinment\r\n36.awesome-feedback\r\n37.contact\r\n38.pre-loader.scss\r\n39.back-to-top\r\n40. Print Css\r\n\r\n\r\n\r\n==============================================  */\r\n\r\n\r\n/* Default  */\r\n\r\n@import'default/variables';\r\n@import'default/typography';\r\n@import'default/spacing';\r\n@import'default/reset';\r\n@import'default/forms';\r\n@import'default/mixins';\r\n@import'default/shortcode';\r\n@import'default/animations';\r\n@import'default/text-animation';\r\n@import'default/sal';\r\n\r\n/* elements */\r\n\r\n\r\n@import'elements/common.scss';\r\n@import'elements/left-sidebar';\r\n@import'elements/profile-setting';\r\n@import'elements/vendor';\r\n@import'elements/button';\r\n@import'elements/nice-select';\r\n@import'elements/shopping-card';\r\n@import'elements/apexchart';\r\n@import'elements/cart-sidebar';\r\n@import'elements/search-input';\r\n@import'elements/backto-top';\r\n@import'elements/registration';\r\n@import'elements/brand';\r\n@import'elements/order-details';\r\n\r\n\r\n\r\n", "\r\n@import url('https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');\r\n@import url('https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');\r\n\r\n* {\r\n    box-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n    margin: 0;\r\n    padding: 0;\r\n    font-size: 10px;\r\n    overflow: hidden;\r\n    overflow-y: auto;\r\n    scroll-behavior: auto !important;\r\n}\r\n\r\nbody {\r\n    font-size: var(--font-size-b1);\r\n    line-height: var(--line-height-b1);\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n    font-family: \"Barlow\", sans-serif;\r\n    color: var(--color-body);\r\n    font-weight: var(--p-regular);\r\n    position: relative;\r\n    overflow-x: hidden;\r\n    // background: var(--background-color-2);\r\n    @media #{$lg-layout} {\r\n        overflow-x: hidden;\r\n    }\r\n    @media #{$md-layout} {\r\n        overflow-x: hidden;\r\n    }\r\n    @media #{$sm-layout} {\r\n        overflow-x: hidden;\r\n    }\r\n    &::before {\r\n        content: \"\";\r\n        width: 100%;\r\n        height: 100%;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        // background-image: url(\"../images/bg/noise.gif\");\r\n        z-index: -1;\r\n        // background-color: rebeccapurple;\r\n        opacity: 0.05;\r\n    }\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6,\r\naddress,\r\np,\r\npre,\r\nblockquote,\r\nmenu,\r\nol,\r\nul,\r\ntable,\r\nhr {\r\n    margin: 0;\r\n    margin-bottom: 20px;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n    word-break: break-word;\r\n    font-family: \"Barlow\", sans-serif;\r\n    line-height: 1.4074;\r\n    color: var(--color-heading-1);\r\n}\r\n\r\nh1,\r\n.h1 {\r\n    font-size: var(--h1);\r\n    line-height: 1.1;\r\n    font-weight: 700;\r\n}\r\n\r\nh2,\r\n.h2 {\r\n    font-size: var(--h2);\r\n    line-height: 1.23;\r\n}\r\n\r\nh3,\r\n.h3 {\r\n    font-size: var(--h3);\r\n    line-height: 54px;\r\n}\r\n\r\nh4,\r\n.h4 {\r\n    font-size: var(--h4);\r\n    line-height: 1.25;\r\n}\r\n\r\nh5,\r\n.h5 {\r\n    font-size: var(--h5);\r\n    line-height: 1.24;\r\n}\r\n\r\nh6,\r\n.h6 {\r\n    font-size: var(--h6);\r\n    line-height: 1.25;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n    a {\r\n        color: inherit;\r\n    }\r\n}\r\n\r\n.bg-color-tertiary {\r\n\r\n    h1,\r\n    h2,\r\n    h3,\r\n    h4,\r\n    h5,\r\n    h6,\r\n    .h1,\r\n    .h2,\r\n    .h3,\r\n    .h4,\r\n    .h5,\r\n    .h6 {\r\n        color: #fff;\r\n    }\r\n\r\n    p {\r\n        color: #6c7279;\r\n\r\n    }\r\n\r\n    a {\r\n        color: #6c7279;\r\n    }\r\n}\r\n\r\n@media #{$md-layout} {\r\n\r\n    h1,\r\n    .h1 {\r\n        font-size: 38px;\r\n    }\r\n\r\n    h2,\r\n    .h2 {\r\n        font-size: 32px;\r\n    }\r\n\r\n    h3,\r\n    .h3 {\r\n        font-size: 28px;\r\n    }\r\n\r\n    h4,\r\n    .h4 {\r\n        font-size: 24px;\r\n    }\r\n\r\n    h5,\r\n    .h5 {\r\n        font-size: 18px;\r\n    }\r\n}\r\n\r\n\r\n@media #{$sm-layout} {\r\n\r\n    h1,\r\n    .h1 {\r\n        font-size: 34px;\r\n    }\r\n\r\n    h2,\r\n    .h2 {\r\n        font-size: 28px;\r\n    }\r\n\r\n    h3,\r\n    .h3 {\r\n        font-size: 24px;\r\n    }\r\n\r\n    h4,\r\n    .h4 {\r\n        font-size: 20px;\r\n    }\r\n\r\n    h5,\r\n    .h5 {\r\n        font-size: 20px;\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\nh1,\r\n.h1,\r\nh2,\r\n.h2,\r\nh3,\r\n.h3 {\r\n    font-weight: var(--s-bold);\r\n}\r\n\r\n\r\nh4,\r\n.h4,\r\nh5,\r\n.h5 {\r\n    font-weight: var(--s-bold);\r\n}\r\n\r\nh6,\r\n.h6 {\r\n    font-weight: var(--s-bold);\r\n}\r\n\r\np {\r\n    font-size: var(--font-size-b2);\r\n    line-height: var(--line-height-b2);\r\n    font-weight: var(--p-regular);\r\n    color: var(--color-body);\r\n    margin: 0 0 40px;\r\n\r\n    @media #{$sm-layout} {\r\n        margin: 0 0 20px;\r\n        font-size: 16px;\r\n        line-height: 28px;\r\n    }\r\n    &.b1 {\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n    }\r\n\r\n    &.b3 {\r\n        font-size: var(--font-size-b3);\r\n        line-height: var(--line-height-b3);\r\n    }\r\n    \r\n    &.has-large-font-size {\r\n        line-height: 1.5;\r\n        font-size: 36px;\r\n    }\r\n\r\n    &.has-medium-font-size {\r\n        font-size: 24px;\r\n        line-height: 36px;\r\n    }\r\n\r\n    &.has-small-font-size {\r\n        font-size: 13px;\r\n    }\r\n\r\n    &.has-very-light-gray-color {\r\n        color: var(--color-white);\r\n    }\r\n\r\n    &.has-background {\r\n        padding: 20px 30px;\r\n    }\r\n\r\n    &.b1 {\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n    }\r\n\r\n    &.b2 {\r\n        font-size: var(--font-size-b2);\r\n        line-height: var(--line-height-b2);\r\n    }\r\n\r\n    &.b3 {\r\n        font-size: var(--font-size-b3);\r\n        line-height: var(--line-height-b3);\r\n    }\r\n\r\n    &:last-child {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n.b1 {\r\n    font-size: var(--font-size-b1);\r\n    line-height: var(--line-height-b1);\r\n}\r\n\r\n.b2 {\r\n    font-size: var(--font-size-b2);\r\n    line-height: var(--line-height-b2);\r\n}\r\n\r\n.b3 {\r\n    font-size: var(--font-size-b3);\r\n    line-height: var(--line-height-b3);\r\n}\r\n\r\n.b4 {\r\n    font-size: var(--font-size-b4);\r\n    line-height: var(--line-height-b4);\r\n}\r\n\r\ntable {\r\n    border-collapse: collapse;\r\n    border-spacing: 0;\r\n    margin: 0 0 20px;\r\n    width: 100%;\r\n}\r\n\r\ntable a,\r\ntable a:link,\r\ntable a:visited {\r\n    text-decoration: none;\r\n}\r\n\r\ncite,\r\n.wp-block-pullquote cite,\r\n.wp-block-pullquote.is-style-solid-color blockquote cite,\r\n.wp-block-quote cite {\r\n    color: var(--color-heading);\r\n}\r\n\r\nvar {\r\n    font-family: 'Exo', sans-serif;\r\n}\r\n\r\n/*---------------------------\r\n\tList Style \r\n---------------------------*/\r\nul,\r\nol {\r\n    padding-left: 18px;\r\n}\r\n\r\nul {\r\n    list-style: square;\r\n    margin-bottom: 30px;\r\n    padding-left: 20px;\r\n\r\n    &.liststyle {\r\n        &.bullet {\r\n            li {\r\n                font-size: 18px;\r\n                line-height: 30px;\r\n                color: var(--color-body);\r\n                position: relative;\r\n                padding-left: 30px;\r\n\r\n                @media #{$sm-layout} {\r\n                    padding-left: 19px;\r\n                }\r\n\r\n                &::before {\r\n                    position: absolute;\r\n                    content: \"\";\r\n                    width: 6px;\r\n                    height: 6px;\r\n                    border-radius: 100%;\r\n                    background: var(--color-body);\r\n                    left: 0;\r\n                    top: 10px;\r\n                }\r\n\r\n                &+li {\r\n                    margin-top: 8px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    li {\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        margin-top: 10px;\r\n        margin-bottom: 10px;\r\n        color: var(--color-body);\r\n\r\n        a {\r\n            text-decoration: none;\r\n            color: var(--color-gray);\r\n\r\n            &:hover {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n\r\n    ul {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\nol {\r\n    margin-bottom: 30px;\r\n\r\n    li {\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        color: var(--color-body);\r\n        margin-top: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        a {\r\n            color: var(--color-heading);\r\n            text-decoration: none;\r\n\r\n            &:hover {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n\r\n    ul {\r\n        padding-left: 30px;\r\n    }\r\n}\r\n\r\n\r\n\r\n.typo-title-area{\r\n    .title{\r\n        margin-top: 0;\r\n    }\r\n}\r\n.paragraph-area{\r\n    p.disc{\r\n        margin-bottom: 20px;\r\n        color: #fff;\r\n    }\r\n}\r\n\r\nh1{\r\n    @media #{$smlg-device} {\r\n        font-size: 64px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        font-size: 54px;\r\n    }\r\n    @media #{$md-layout} {\r\n        font-size: 40px;\r\n        line-height: 56px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        font-size: 30px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$large-mobile} {\r\n        font-size: 28px;\r\n        line-height: 36px;\r\n    }\r\n    @media #{$small-mobile} {\r\n        font-size: 26px;\r\n        line-height: 1.3;\r\n    }\r\n}\r\nh2{\r\n    @media #{$smlg-device} {\r\n        font-size: 54px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        font-size: 44px;\r\n    }\r\n    @media #{$md-layout} {\r\n        font-size: 36px;\r\n        line-height: 56px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        font-size: 30px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$large-mobile} {\r\n        font-size: 26px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$small-mobile} {\r\n        font-size: 24px;\r\n        line-height: 1.3;\r\n    }\r\n}\r\nh3{\r\n    @media #{$smlg-device} {\r\n        font-size: 40px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        font-size: 36px;\r\n    }\r\n    @media #{$md-layout} {\r\n        font-size: 30px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$sm-layout} {\r\n        font-size: 30px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$large-mobile} {\r\n        font-size: 24px;\r\n        line-height: 1.3;\r\n    }\r\n    @media #{$small-mobile} {\r\n        font-size: 22px;\r\n        line-height: 1.3;\r\n    }\r\n}", ":root {\r\n\r\n    // themes color\r\n    --color-primary: #629D23;\r\n    --color-secondary: #1F1F25;\r\n    \r\n    // body color\r\n    --color-body: #6E777D;\r\n\r\n    // title color\r\n    --color-heading-1: #2C3C28;\r\n\r\n    --color-white:#fff;\r\n\r\n    // notify Colors\r\n    --color-success: #3EB75E;\r\n    --color-danger: #DC2626;\r\n    --color-warning: #FF8F3C;\r\n    --color-info: #1BA2DB;\r\n    \r\n\r\n    //Social icon colors\r\n    --color-facebook: #3B5997;\r\n    --color-twitter: #1BA1F2;\r\n    --color-youtube: #ED4141;\r\n    --color-linkedin: #0077B5;\r\n    --color-pinterest: #E60022;\r\n    --color-instagram: #C231A1;\r\n    --color-vimeo: #00ADEF;\r\n    --color-twitch: #6441A3;\r\n    --color-discord: #7289da;\r\n\r\n\r\n    // Font weight\r\n    --p-light: 300;\r\n    --p-regular: 400;\r\n    --p-medium: 500;\r\n    --p-semi-bold: 600;\r\n    --p-bold: 700;\r\n    --p-extra-bold: 800;\r\n    --p-black: 900;\r\n\r\n    // Font weight\r\n    --s-light: 300;\r\n    --s-regular: 400;\r\n    --s-medium: 500;\r\n    --s-semi-bold: 600;\r\n    --s-bold: 700;\r\n    --s-extra-bold: 800;\r\n    --s-black: 900;\r\n\r\n\r\n\r\n    //transition easing\r\n    --transition: 0.3s;\r\n\r\n\r\n    // Font Family\r\n    --font-primary: \"Barlow\", sans-serif;\r\n    --font-secondary: \"Barlow\", sans-serif;\r\n    --font-three: \"FontAwesome\";\r\n\r\n    //Fonts Size\r\n    --font-size-b1: 16px;\r\n\r\n    --font-size-b2: 16px;\r\n    --font-size-b3: 14px;\r\n\r\n\r\n    //Line Height\r\n    --line-height-b1: 1.3;\r\n\r\n    --line-height-b2: 1.3;\r\n    --line-height-b3: 1.3;\r\n\r\n\r\n    // Heading Font\r\n    --h1: 60px;\r\n    --h2: 30px;\r\n    --h3: 26px;\r\n    --h4: 18px;\r\n    --h5: 16px;\r\n    --h6: 15px;\r\n\r\n}\r\n\r\n// Layouts Variation\r\n$smlg-device: 'only screen and (max-width: 1199px)';\r\n$extra-device: 'only screen and (min-width: 1600px) and (max-width: 1919px)';\r\n$laptop-device: 'only screen and (min-width: 1200px) and (max-width: 1599px)';\r\n$lg-layout: 'only screen and (min-width: 992px) and (max-width: 1199px)';\r\n$md-layout:'only screen and (min-width: 768px) and (max-width: 991px)';\r\n$sm-layout:'only screen and (max-width: 767px)';\r\n$large-mobile: 'only screen and (max-width: 575px)';\r\n$small-mobile: 'only screen and (max-width: 479px)';", "/*=========================\r\n    Section Separation \r\n==========================*/\r\n\r\n\r\n.rts-section-gap {\r\n    padding: 60px 0;\r\n    @media #{$md-layout} {\r\n        padding: 60px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0;\r\n    }\r\n}\r\n\r\n.rts-section-gapBottom {\r\n    padding-bottom: 60px;\r\n\r\n    @media #{$md-layout} {\r\n        padding-bottom: 60px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 60px;\r\n    }\r\n}\r\n\r\n.rts-section-gapTop {\r\n    padding-top: 60px;\r\n    @media #{$md-layout} {\r\n        padding-top: 70px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-top: 60px;\r\n    }\r\n}\r\n\r\n.rts-section-gap2 {\r\n    padding: 100px 0;\r\n    @media #{$md-layout} {\r\n        padding: 100px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 100px 0;\r\n    }\r\n}\r\n\r\n.rts-section-gap2Bottom {\r\n    padding-bottom: 100px;\r\n\r\n    @media #{$md-layout} {\r\n        padding-bottom: 100px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 100px;\r\n    }\r\n}\r\n\r\n.rts-section-gap2Top {\r\n    padding-top: 100px;\r\n    @media #{$md-layout} {\r\n        padding-top: 100px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-top: 100px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.pl--0 {\r\n    padding-left: 0 !important;\r\n}\r\n\r\n.pr--0 {\r\n    padding-right: 0 !important;\r\n}\r\n\r\n.pt--0 {\r\n    padding-top: 0 !important;\r\n}\r\n\r\n.pb--0 {\r\n    padding-bottom: 0 !important;\r\n}\r\n\r\n.mr--0 {\r\n    margin-right: 0 !important;\r\n}\r\n\r\n.ml--0 {\r\n    margin-left: 0 !important;\r\n}\r\n\r\n.mt--0 {\r\n    margin-top: 0 !important;\r\n}\r\n\r\n.mb--0 {\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n@for $i from 1 through 40 {\r\n    .ptb--#{5 * $i} { padding: 5px *$i 0 !important;}\r\n    .plr--#{5 * $i} { padding: 0 5px *$i !important;}\r\n    .pt--#{5 * $i} { padding-top: 5px *$i !important;}\r\n    .pb--#{5 * $i} { padding-bottom: 5px *$i !important;}\r\n    .pl--#{5 * $i} { padding-left: 5px *$i !important;}\r\n    .pr--#{5 * $i} { padding-right: 5px *$i !important;}\r\n    .mt--#{5 * $i} {margin-top: 5px *$i !important;}\r\n    .mb--#{5 * $i} {margin-bottom: 5px *$i !important;}\r\n    .mr--#{5 * $i} {margin-right: 5px *$i !important;}\r\n    .ml--#{5 * $i} {margin-left: 5px *$i !important;}\r\n}\r\n\r\n@media #{$laptop-device} {\r\n    @for $i from 1 through 40 {\r\n        .ptb_lp--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n\r\n        .plr_lp--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n\r\n        .pt_lp--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_lp--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_lp--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_lp--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_lp--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_lp--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n}\r\n\r\n@media #{$lg-layout} {\r\n    @for $i from 1 through 40 {\r\n        .ptb_lg--#{5 * $i} {\r\n            padding: 5px *$i 0 !important;\r\n        }\r\n\r\n        .plr_lg--#{5 * $i} {\r\n            padding: 0 5px *$i !important;\r\n        }\r\n\r\n        .pt_lg--#{5 * $i} {\r\n            padding-top: 5px *$i !important;\r\n        }\r\n\r\n        .pb_lg--#{5 * $i} {\r\n            padding-bottom: 5px *$i !important;\r\n        }\r\n\r\n        .pl_lg--#{5 * $i} {\r\n            padding-left: 5px *$i !important;\r\n        }\r\n\r\n        .pr_lg--#{5 * $i} {\r\n            padding-right: 5px *$i !important;\r\n        }\r\n\r\n        .mt_lg--#{5 * $i} {\r\n            margin-top: 5px *$i !important;\r\n        }\r\n\r\n        .mb_lg--#{5 * $i} {\r\n            margin-bottom: 5px *$i !important;\r\n        }\r\n\r\n        .ml_lg--#{5 * $i} {\r\n            margin-left: 5px *$i !important;\r\n        }\r\n\r\n    }\r\n}\r\n\r\n@media #{$md-layout} {\r\n\r\n    .ptb_md--0 {\r\n        padding: 0 !important;\r\n    }\r\n\r\n    .pl_md--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n\r\n    .pr_md--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n\r\n    .pt_md--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n\r\n    .pb_md--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n\r\n    .mr_md--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n\r\n    .ml_md--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    .mt_md--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n\r\n    .mb_md--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n\r\n    .ptb_md--250 {\r\n        padding: 250px 0 !important;\r\n    }\r\n\r\n\r\n    @for $i from 1 through 40 {\r\n        .ptb_md--#{5 * $i} {\r\n            padding: 5px *$i 0 !important;\r\n        }\r\n\r\n        .plr_md--#{5 * $i} {\r\n            padding: 0 5px *$i !important;\r\n        }\r\n\r\n        .pt_md--#{5 * $i} {\r\n            padding-top: 5px *$i !important;\r\n        }\r\n\r\n        .pb_md--#{5 * $i} {\r\n            padding-bottom: 5px *$i !important;\r\n        }\r\n\r\n        .pl_md--#{5 * $i} {\r\n            padding-left: 5px *$i !important;\r\n        }\r\n\r\n        .pr_md--#{5 * $i} {\r\n            padding-right: 5px *$i !important;\r\n        }\r\n\r\n        .mt_md--#{5 * $i} {\r\n            margin-top: 5px *$i !important;\r\n        }\r\n\r\n        .mb_md--#{5 * $i} {\r\n            margin-bottom: 5px *$i !important;\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\n@media #{$sm-layout} {\r\n    .ptb_sm--250 {\r\n        padding: 250px 0 !important;\r\n    }\r\n\r\n    .ptb_sm--0 {\r\n        padding: 0 !important;\r\n    }\r\n\r\n    .pl_sm--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n\r\n    .pr_sm--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n\r\n    .pt_sm--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n\r\n    .pb_sm--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n\r\n    .mr_sm--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n\r\n    .ml_sm--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    .mt_sm--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n\r\n    .mb_sm--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n\r\n    .pt_sm--150 {\r\n        padding-top: 150px !important;\r\n    }\r\n\r\n    .pb_sm--110 {\r\n        padding-bottom: 110px !important;\r\n    }\r\n\r\n    @for $i from 1 through 40 {\r\n        .ptb_sm--#{5 * $i} {\r\n            padding: 5px *$i 0 !important;\r\n        }\r\n\r\n        .plr_sm--#{5 * $i} {\r\n            padding: 0 5px *$i !important;\r\n        }\r\n\r\n        .pt_sm--#{5 * $i} {\r\n            padding-top: 5px *$i !important;\r\n        }\r\n\r\n        .pb_sm--#{5 * $i} {\r\n            padding-bottom: 5px *$i !important;\r\n        }\r\n\r\n        .pl_sm--#{5 * $i} {\r\n            padding-left: 5px *$i !important;\r\n        }\r\n\r\n        .pr_sm--#{5 * $i} {\r\n            padding-right: 5px *$i !important;\r\n        }\r\n\r\n        .mt_sm--#{5 * $i} {\r\n            margin-top: 5px *$i !important;\r\n        }\r\n\r\n        .ml_sm--#{5 * $i} {\r\n            margin-left: 5px *$i !important;\r\n        }\r\n\r\n        .mr_sm--#{5 * $i} {\r\n            margin-right: 5px *$i !important;\r\n        }\r\n\r\n        .mb_sm--#{5 * $i} {\r\n            margin-bottom: 5px *$i !important;\r\n        }\r\n    }\r\n\r\n    .pl_sm--0 {\r\n        padding-left: 0;\r\n    }\r\n\r\n    .pr_sm--0 {\r\n        padding-right: 0;\r\n    }\r\n\r\n    .pt_sm--0 {\r\n        padding-top: 0;\r\n    }\r\n\r\n    .pb_sm--0 {\r\n        padding-bottom: 0;\r\n    }\r\n\r\n    .mr_sm--0 {\r\n        margin-right: 0;\r\n    }\r\n\r\n    .ml_sm--0 {\r\n        margin-left: 0;\r\n    }\r\n\r\n    .mt_sm--0 {\r\n        margin-top: 0;\r\n    }\r\n\r\n    .mb_sm--0 {\r\n        margin-bottom: 0;\r\n    }\r\n\r\n}\r\n\r\n@media #{$large-mobile} {\r\n    @for $i from 1 through 20 {\r\n        .ptb_mobile--#{5 * $i} {\r\n            padding: 5px *$i 0 !important;\r\n        }\r\n\r\n        .plr_mobile--#{5 * $i} {\r\n            padding: 0 5px *$i !important;\r\n        }\r\n\r\n        .pt_mobile--#{5 * $i} {\r\n            padding-top: 5px *$i !important;\r\n        }\r\n\r\n        .pb_mobile--#{5 * $i} {\r\n            padding-bottom: 5px *$i !important;\r\n        }\r\n\r\n        .pl_mobile--#{5 * $i} {\r\n            padding-left: 5px *$i !important;\r\n        }\r\n\r\n        .pr_mobile--#{5 * $i} {\r\n            padding-right: 5px *$i !important;\r\n        }\r\n\r\n        .mt_mobile--#{5 * $i} {\r\n            margin-top: 5px *$i !important;\r\n        }\r\n\r\n        .mb_mobile--#{5 * $i} {\r\n            margin-bottom: 5px *$i !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@for $i from 1 through 20 {\r\n    .slick-gutter-#{$i * 5} {\r\n        margin-left: -#{$i * 5}px;\r\n        margin-right: -#{$i * 5}px;\r\n\r\n        .slick-slide {\r\n            padding-left: #{$i * 5}px;\r\n            padding-right: #{$i * 5}px;\r\n        }\r\n    }\r\n}\r\n\r\n.mt-dec-30{\r\n    margin-top: -30px !important;\r\n}\r\n\r\n.mt_dec--30 {\r\n    margin-top: -30px !important;\r\n}\r\n\r\n.mt-dec-100{\r\n    margin-top: -100px !important;\r\n}\r\n\r\n.small-margin-pricing{\r\n    @media #{$small-mobile} {\r\n        margin-bottom: 25px !important;\r\n    }\r\n}\r\n\r\n.contact-input{\r\n    @media #{$small-mobile} {\r\n        margin-bottom: 35px;\r\n    }\r\n}\r\n.mb_dec--35{\r\n    @media #{$md-layout} {\r\n        margin-bottom: -50px\r\n    }\r\n}\r\n\r\n\r\n.mb_dec--35{\r\n    @media #{$sm-layout} {\r\n        margin-bottom: -75px;\r\n    }\r\n    @media #{$large-mobile} {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n.mt-contact-sm{\r\n    @media #{$large-mobile} {\r\n        margin-top: 30px !important;\r\n    }\r\n}\r\n\r\n.testimonial-pb{\r\n    @media #{$lg-layout} {\r\n        padding-bottom: 35px;\r\n    }\r\n}\r\n\r\n.contact-input{\r\n    @media #{$md-layout} {\r\n        padding-bottom: 30px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 30px;\r\n    }\r\n}\r\n\r\n.pb_xl--130{\r\n    padding-bottom: 130px;\r\n    @media #{$smlg-device} {\r\n        padding-bottom: 110px;\r\n    }\r\n}\r\n\r\n\r\n.mt_experience{\r\n    @media #{$smlg-device} {\r\n        margin-top: -10px;\r\n    }\r\n}\r\n\r\n.mt_dec--120{\r\n    margin-top: -60px;\r\n}\r\n\r\n\r\n.plr_md--0{\r\n    @media #{$md-layout} {\r\n        padding-left: 0 !important;\r\n        padding-right: 0 !important; \r\n    }\r\n}\r\n\r\n.padding-contorler-am-slide{\r\n    padding-left: 246px;\r\n    @media #{$laptop-device} {\r\n        padding-left: 100px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding-left: 100px;\r\n    }\r\n    @media #{$md-layout} {\r\n        padding-left: 50px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-left: 15px;\r\n    }\r\n}\r\n.padding-contorler-am-slide-11{\r\n    padding-left: 246px;\r\n    @media #{$laptop-device} {\r\n        padding-left: 100px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding-left: 0;\r\n    }\r\n}\r\n.padding-contorler-am-slide-right{\r\n    padding-right: 200px;\r\n    @media #{$laptop-device} {\r\n        padding-right: 30px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding-right: 30px;\r\n        padding-top: 50px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-right: 30px;\r\n        padding-top: 50px;\r\n    }\r\n}\r\n\r\n.g-24{\r\n    --bs-gutter-x: 24px;\r\n    --bs-gutter-y: 24px;\r\n}\r\n.g-40{\r\n    --bs-gutter-x: 40px;\r\n    --bs-gutter-y: 40px;\r\n}\r\n", "*{\r\n    -webkit-box-sizing: border-box;\r\n    -moz-box-sizing: border-box;\r\n    box-sizing: border-box;\r\n}\r\n\r\narticle,\r\naside,\r\ndetails,\r\nfigcaption,\r\nfigure,\r\nfooter,\r\nheader,\r\nnav,\r\nsection,\r\nsummary {\r\n    display: block;\r\n}\r\n\r\n\r\naudio,\r\ncanvas,\r\nvideo {\r\n    display: inline-block;\r\n}\r\n\r\naudio:not([controls]) {\r\n    display: none;\r\n    height: 0;\r\n}\r\n\r\n[hidden] {\r\n    display: none;\r\n}\r\n\r\na {\r\n    color: var(--color-heading);\r\n    text-decoration: none;\r\n    outline: none;\r\n}\r\n\r\n\r\na:hover,\r\na:focus,\r\na:active {\r\n    text-decoration: none;\r\n    outline: none;\r\n    color: var(--color-primary);\r\n}\r\n\r\na:focus {\r\n    outline: none;\r\n}\r\n\r\naddress {\r\n    margin: 0 0 24px;\r\n}\r\n\r\nabbr[title] {\r\n    border-bottom: 1px dotted;\r\n}\r\n\r\nb,\r\nstrong {\r\n    font-weight: bold;\r\n}\r\n\r\nmark {\r\n    background: var(--color-primary);\r\n    color: #ffffff;\r\n}\r\n\r\n\r\ncode,\r\nkbd,\r\npre,\r\nsamp {\r\n    font-size: var(--font-size-b3);\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    color: var(--color-primary);\r\n}\r\n\r\nkbd,\r\nins {\r\n    color: #ffffff;\r\n}\r\n\r\npre {\r\n    font-family: 'Raleway', sans-serif;\r\n    font-size: var(--font-size-b3);\r\n    margin: 10px 0;\r\n    overflow: auto;\r\n    padding: 20px;\r\n    white-space: pre;\r\n    white-space: pre-wrap;\r\n    word-wrap: break-word;\r\n    color: var(--color-body);\r\n    background: var(--color-lighter);\r\n}\r\n\r\nsmall {\r\n    font-size: smaller;\r\n}\r\n\r\nsub,\r\nsup {\r\n    font-size: 75%;\r\n    line-height: 0;\r\n    position: relative;\r\n    vertical-align: baseline;\r\n}\r\n\r\nsup {\r\n    top: -0.5em;\r\n}\r\n\r\nsub {\r\n    bottom: -0.25em;\r\n}\r\n\r\ndl {\r\n    margin-top: 0;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\n\r\ndt {\r\n    font-weight: bold;\r\n    color: var(--color-heading);\r\n}\r\n\r\nmenu,\r\nol,\r\nul {\r\n    margin: 16px 0;\r\n    padding: 0 0 0 40px;\r\n}\r\n\r\nnav ul,\r\nnav ol {\r\n    list-style: none;\r\n    list-style-image: none;\r\n}\r\n\r\nli>ul,\r\nli>ol {\r\n    margin: 0;\r\n}\r\n\r\nol {\r\n    ul {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\nimg {\r\n    -ms-interpolation-mode: bicubic;\r\n    border: 0;\r\n    vertical-align: middle;\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\nsvg:not(:root) {\r\n    overflow: hidden;\r\n}\r\n\r\nfigure {\r\n    margin: 0;\r\n}\r\n\r\nform {\r\n    margin: 0;\r\n}\r\n\r\nfieldset {\r\n    border: 1px solid var(--color-border);\r\n    margin: 0 2px;\r\n    min-width: inherit;\r\n    padding: 0.35em 0.625em 0.75em;\r\n}\r\n\r\nlegend {\r\n    border: 0;\r\n    padding: 0;\r\n    white-space: normal;\r\n}\r\n\r\nbutton,\r\ninput,\r\nselect,\r\ntextarea {\r\n    font-size: 100%;\r\n    margin: 0;\r\n    max-width: 100%;\r\n    vertical-align: baseline;\r\n}\r\n\r\nbutton,\r\ninput {\r\n    line-height: normal;\r\n}\r\n\r\nbutton,\r\nhtml input[type=\"button\"],\r\ninput[type=\"reset\"],\r\ninput[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n    -moz-appearance: button;\r\n    appearance: button;\r\n    cursor: pointer;\r\n}\r\n\r\nbutton[disabled],\r\ninput[disabled] {\r\n    cursor: default;\r\n}\r\n\r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    padding: 0;\r\n}\r\n\r\ninput[type=\"search\"] {\r\n    -webkit-appearance: textfield;\r\n    -moz-appearance: textfield;\r\n    appearance: textfield;\r\n    appearance: textfield;\r\n    padding-right: 2px;\r\n    width: 270px;\r\n}\r\n\r\ninput[type=\"search\"]::-webkit-search-decoration {\r\n    -webkit-appearance: none;\r\n    appearance: none;\r\n}\r\n\r\nbutton::-moz-focus-inner,\r\ninput::-moz-focus-inner {\r\n    border: 0;\r\n    padding: 0;\r\n}\r\n\r\ntextarea {\r\n    overflow: auto;\r\n    vertical-align: top;\r\n}\r\n\r\ncaption,\r\nth,\r\ntd {\r\n    font-weight: normal;\r\n}\r\n\r\nth {\r\n    font-weight: 500;\r\n    text-transform: uppercase;\r\n}\r\n\r\ntd,\r\n.wp-block-calendar tfoot td {\r\n    border: 1px solid var(--color-border);\r\n    padding: 7px 10px;\r\n}\r\n\r\ndel {\r\n    color: #333;\r\n}\r\n\r\nins {\r\n    background: rgba(255, 47, 47, 0.4);\r\n    text-decoration: none;\r\n}\r\n\r\nhr {\r\n    background-size: 4px 4px;\r\n    border: 0;\r\n    height: 1px;\r\n    margin: 0 0 24px;\r\n}\r\n\r\ntable a,\r\ntable a:link,\r\ntable a:visited {\r\n    text-decoration: underline;\r\n}\r\n\r\ndt {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\n\r\ncaption {\r\n    caption-side: top;\r\n}\r\n\r\nkbd {\r\n    background: var(--heading-color);\r\n}\r\n\r\ndfn,\r\ncite,\r\nem {\r\n    font-style: italic;\r\n}\r\n\r\n\r\n/* BlockQuote  */\r\nblockquote,\r\nq {\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    quotes: none;\r\n}\r\n\r\nblockquote:before,\r\nblockquote:after,\r\nq:before,\r\nq:after {\r\n    content: \"\";\r\n    content: none;\r\n}\r\n\r\nblockquote {\r\n    font-size: var(--font-size-b1);\r\n    font-style: italic;\r\n    font-weight: var(--p-light);\r\n    margin: 24px 40px;\r\n}\r\n\r\nblockquote blockquote {\r\n    margin-right: 0;\r\n}\r\n\r\nblockquote cite,\r\nblockquote small {\r\n    font-size: var(--font-size-b3);\r\n    font-weight: normal;\r\n}\r\n\r\nblockquote strong,\r\nblockquote b {\r\n    font-weight: 700;\r\n}\r\n", "/* ========= Forms Styles ========= */\r\ninput,\r\nbutton,\r\nselect,\r\ntextarea {\r\n    background: transparent;\r\n    border: 1px solid var(--color-border);\r\n    transition: all 0.4s ease-out 0s;\r\n    color: var(--color-body);\r\n    width: 100%;\r\n\r\n    &:focus,\r\n    &:active {\r\n        outline: none;\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\nbutton,\r\n[type=\"button\"],\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n}\r\n\r\ninput {\r\n    height: 40px;\r\n    padding: 0 15px;\r\n}\r\n\r\n// select {\r\n//     transition: 0.3s;\r\n//     height: 40px;\r\n//     padding: 0 20px;\r\n//     outline: none;\r\n//     color: var(--color-body);\r\n//     -moz-appearance: none;\r\n//     -webkit-appearance: none;\r\n//     appearance: none;\r\n//     border: 1px solid var(--color-border);\r\n//     border-radius: 4px;\r\n//     background: url(../images/icons/arrow-icon.png) 95% center no-repeat transparent;\r\n//     padding-right: 32px;\r\n// }\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"password\"],\r\ninput[type=\"email\"],\r\ninput[type=\"number\"],\r\ninput[type=\"tel\"],\r\ntextarea {\r\n    font-size: var(--font-size-b2);\r\n    font-weight: 400;\r\n    height: auto;\r\n    line-height: 28px;\r\n    background: transparent;\r\n    -webkit-box-shadow: none;\r\n    box-shadow: none;\r\n    padding: 0 15px;\r\n    outline: none;\r\n    border: var(--border-width) solid var(--color-border);\r\n    border-radius: var(--radius);\r\n\r\n    /* -- Placeholder -- */\r\n    &::placeholder {\r\n        color: var(--body-color);\r\n        /* Firefox */\r\n        opacity: 1;\r\n    }\r\n\r\n    &:-ms-input-placeholder {\r\n        /* Internet Explorer 10-11 */\r\n        color: var(--body-color);\r\n    }\r\n\r\n    &::-ms-input-placeholder {\r\n        /* Microsoft Edge */\r\n        color: var(--body-color);\r\n    }\r\n\r\n    &.p-holder__active {\r\n        border-color: var(--color-primary);\r\n\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: var(--color-primary);\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: var(--color-primary);\r\n        }\r\n\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: var(--color-primary);\r\n        }\r\n    }\r\n\r\n    &.p-holder__error {\r\n        border-color: #f4282d;\r\n\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: #f4282d;\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: #f4282d;\r\n        }\r\n\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: #f4282d;\r\n        }\r\n\r\n        &:focus {\r\n            border-color: #f4282d;\r\n        }\r\n    }\r\n\r\n    &:focus {\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.input-active {\r\n    @extend .p-holder__active;\r\n\r\n    input {\r\n        @extend .p-holder__active;\r\n    }\r\n}\r\n\r\n.input-error {\r\n    @extend .p-holder__error;\r\n\r\n    input {\r\n        @extend .p-holder__error;\r\n    }\r\n}\r\n\r\n// Custom Checkbox and radio button \r\n// Custom Checkbox and radio button \r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    opacity: 1;\r\n    position: relative;\r\n    height: auto !important;\r\n    max-width: 18px;\r\n    width: max-content;\r\n    ~label {\r\n        position: relative;\r\n        font-size: 14px;\r\n        line-height: 17px;\r\n        color: #2C3C28;\r\n        font-weight: 500;\r\n        padding-left: 25px;\r\n        cursor: pointer;\r\n\r\n        &::before {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 1 px;\r\n            left: 0;\r\n            width: 15px;\r\n            height: 15px;\r\n            background-color: #5d5d7e;\r\n            border-radius: 2px;\r\n            transition: all .3s;\r\n            border-radius: 2px;\r\n        }\r\n\r\n        &::after {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 16%;\r\n            left: 2px;\r\n            width: 10px;\r\n            height: 6px;\r\n            background-color: transparent;\r\n            border-bottom: 2px solid #b8b8b8;\r\n            border-left: 2px solid #b8b8b8;\r\n            border-radius: 2px;\r\n            transform: rotate(-45deg);\r\n            opacity: 0;\r\n            transition: all .3s;\r\n        }\r\n    }\r\n\r\n    &:checked {\r\n        ~label {\r\n\r\n            &::after {\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\ninput:checked ~ .rn-check-box-label {\r\n    &::before{\r\n        background: var(--color-primary) !important;\r\n    }\r\n}\r\n\r\n\r\ninput[type=\"radio\"] {\r\n    ~label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n\r\n        &::after {\r\n            width: 8px;\r\n            height: 8px;\r\n            left: 3px;\r\n            background: #fff;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 20px;\r\n\r\n    label {\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        line-height: 22px;\r\n        font-weight: 500;\r\n    }\r\n\r\n    input {\r\n        border: 0 none;\r\n        border-radius: 4px;\r\n        height: 50px;\r\n        font-size: var(--font-size-b2);\r\n        transition: var(--transition);\r\n        padding: 0 20px;\r\n        background-color: var(--color-lightest);\r\n        border: 1px solid transparent;\r\n        transition: var(--transition);\r\n\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n            box-shadow: none;\r\n        }\r\n    }\r\n\r\n    textarea {\r\n        min-height: 160px;\r\n        border: 0 none;\r\n        border-radius: 4px;\r\n        resize: none;\r\n        padding: 15px;\r\n        font-size: var(--font-size-b2);\r\n        transition: var(--transition);\r\n        background-color: var(--color-lightest);\r\n        border: 1px solid transparent;\r\n\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n\r\ninput[type=\"submit\"] {\r\n    width: auto;\r\n    padding: 0 30px;\r\n    border-radius: 500px;\r\n    display: inline-block;\r\n    font-weight: 500;\r\n    transition: 0.3s;\r\n    height: 60px;\r\n    background: var(--color-primary);\r\n    color: var(--color-white);\r\n    font-weight: var(--p-medium);\r\n    font-size: var(--font-size-b2);\r\n    line-height: var(--line-height-b3);\r\n    height: 50px;\r\n    border: 2px solid var(--color-primary);\r\n    transition: var(--transition);\r\n\r\n    &:hover {\r\n        background: transparent;\r\n        color: var(--color-primary);\r\n        transform: translateY(-5px);\r\n    }\r\n\r\n}", "\r\n/*==============================\r\n *  Utilities\r\n=================================*/\r\n\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n    content: \" \";\r\n    display: table;\r\n}\r\n\r\n.clearfix:after {\r\n    clear: both;\r\n}\r\n\r\n.fix {\r\n    overflow: hidden;\r\n}\r\n\r\n.slick-initialized .slick-slide {\r\n    margin-bottom: -10px;\r\n}\r\n\r\n.slick-gutter-15 {\r\n    margin: -30px -15px;\r\n    .slick-slide {\r\n        padding: 30px 15px;\r\n    }\r\n}\r\n\r\niframe {\r\n    width: 100%;\r\n}\r\n\r\n/*===============================\r\n    Background Color \r\n=================================*/\r\n\r\n@include config-bg-colors('bg-color-',\r\n    'primary'var(--color-primary),\r\n    'secondary'var(--color-secondary),\r\n    'tertiary'var(--color-tertiary),\r\n    'gray'var(--color-gray),\r\n    'white'#FFFFFF,\r\n    'black'#1A1A1A,\r\n    'extra03'var(--color-extra03));\r\n\r\n/*===========================\r\nBackground Image \r\n=============================*/\r\n.bg_image {\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center center;\r\n}\r\n\r\n@for $i from 1 through 40 {\r\n    .bg_image--#{$i} {\r\n        background-image: url(../images/bg/bg-image-#{$i}.jpg);\r\n    }\r\n}\r\n\r\n\r\n/* Height and width */\r\n.fullscreen {\r\n    min-height: 980px;\r\n    width: 100%;\r\n}\r\n\r\n/*===================\r\nCustom Row\r\n======================*/\r\n.row--0 {\r\n    margin-left: -0px;\r\n    margin-right: -0px;\r\n\r\n    &>[class*=\"col\"] {\r\n        padding-left: 0px;\r\n        padding-right: 0px;\r\n    }\r\n}\r\n\r\n.row--5 {\r\n    margin-left: -5px;\r\n    margin-right: -5px;\r\n\r\n    &>[class*=\"col\"] {\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n    }\r\n}\r\n\r\n.row--10 {\r\n    margin-left: -10px;\r\n    margin-right: -10px;\r\n\r\n    &>[class*=\"col\"] {\r\n        padding-left: 10px;\r\n        padding-right: 10px;\r\n    }\r\n}\r\n\r\n.row--20 {\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 20px;\r\n        padding-right: 20px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--25 {\r\n    margin-left: -25px;\r\n    margin-right: -25px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 25px;\r\n        padding-right: 25px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.row--30 {\r\n    margin-left: -30px;\r\n    margin-right: -30px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 30px;\r\n        padding-right: 30px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.row--45 {\r\n    margin-left: -45px;\r\n    margin-right: -45px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 45px;\r\n        padding-right: 45px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.row--40 {\r\n    margin-left: -40px;\r\n    margin-right: -40px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 40px;\r\n        padding-right: 40px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--60 {\r\n    margin-left: -60px;\r\n    margin-right: -60px;\r\n\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 60px;\r\n        padding-right: 60px;\r\n\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n/*===========================\r\n    Input Placeholder\r\n=============================*/\r\ninput:-moz-placeholder,\r\ntextarea:-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-webkit-input-placeholder,\r\ntextarea::-webkit-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-moz-placeholder,\r\ntextarea::-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput:-ms-input-placeholder,\r\ntextarea:-ms-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\n/*=============================\r\n\tOverlay styles \r\n==============================*/\r\n\r\n[data-overlay],\r\n[data-black-overlay],\r\n[data-white-overlay] {\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n[data-overlay]>div,\r\n[data-overlay]>*,\r\n[data-black-overlay]>div,\r\n[data-black-overlay]>*,\r\n[data-white-overlay]>div,\r\n[data-white-overlay]>* {\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n\r\n[data-overlay]:before,\r\n[data-black-overlay]:before,\r\n[data-white-overlay]:before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    z-index: -1;\r\n}\r\n\r\n[data-overlay]:before {\r\n    background: var(--color-primary);\r\n}\r\n\r\n[data-black-overlay]:before {\r\n    background-color: #000000;\r\n}\r\n\r\n[data-white-overlay]:before {\r\n    background-color: #ffffff;\r\n}\r\n\r\n@for $i from 1 through 10 {\r\n\r\n    [data-overlay=\"#{$i}\"]:before,\r\n    [data-black-overlay=\"#{$i}\"]:before,\r\n    [data-white-overlay=\"#{$i}\"]:before {\r\n        opacity: #{$i * 0.10};\r\n    }\r\n    \r\n}\r\n\r\n\r\n\r\n", "\r\n@mixin clearfix() {\r\n    &::after {\r\n        content: \"\";\r\n        clear: both;\r\n        display: table;\r\n    }\r\n}\r\n\r\n@mixin config-bg-colors($prefix, $bg-color-...) {\r\n    @each $i in $bg-color- {\r\n        .#{$prefix}#{nth($i, 1)} {\r\n            background: nth($i, 2);\r\n        }\r\n    }\r\n}\r\n\r\n@mixin placeholder {\r\n    &::-webkit-input-placeholder {\r\n        @content;\r\n    }\r\n\r\n    &:-moz-placeholder {\r\n        @content;\r\n    }\r\n\r\n    &::-moz-placeholder {\r\n        @content;\r\n    }\r\n\r\n    &:-ms-input-placeholder {\r\n        @content;\r\n    }\r\n}\r\n", "/*!\r\nAnimate.css - http://daneden.me/animate\r\nVersion - 3.4.0\r\nLicensed under the MIT license - http://opensource.org/licenses/MIT\r\n\r\nCopyright (c) 2015 <PERSON>\r\n*/\r\n\r\n.animated {\r\n    -webkit-animation-duration: 1s;\r\n    animation-duration: 1s;\r\n    -webkit-animation-fill-mode: both;\r\n    animation-fill-mode: both;\r\n}\r\n\r\n.animated.infinite {\r\n    -webkit-animation-iteration-count: infinite;\r\n    animation-iteration-count: infinite;\r\n}\r\n\r\n.animated.hinge {\r\n    -webkit-animation-duration: 2s;\r\n    animation-duration: 2s;\r\n}\r\n\r\n.animated.bounceIn,\r\n.animated.bounceOut {\r\n    -webkit-animation-duration: .75s;\r\n    animation-duration: .75s;\r\n}\r\n\r\n.animated.flipOutX,\r\n.animated.flipOutY {\r\n    -webkit-animation-duration: .75s;\r\n    animation-duration: .75s;\r\n}\r\n\r\n@-webkit-keyframes bounce {\r\n    from,\r\n    20%,\r\n    53%,\r\n    80%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    40%,\r\n    43% {\r\n        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        -webkit-transform: translate3d(0, -30px, 0);\r\n        transform: translate3d(0, -30px, 0);\r\n    }\r\n\r\n    70% {\r\n        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        -webkit-transform: translate3d(0, -15px, 0);\r\n        transform: translate3d(0, -15px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, -4px, 0);\r\n        transform: translate3d(0, -4px, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounce {\r\n\r\n    from,\r\n    20%,\r\n    53%,\r\n    80%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    40%,\r\n    43% {\r\n        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        -webkit-transform: translate3d(0, -30px, 0);\r\n        transform: translate3d(0, -30px, 0);\r\n    }\r\n\r\n    70% {\r\n        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n        -webkit-transform: translate3d(0, -15px, 0);\r\n        transform: translate3d(0, -15px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, -4px, 0);\r\n        transform: translate3d(0, -4px, 0);\r\n    }\r\n}\r\n\r\n.bounce {\r\n    -webkit-animation-name: bounce;\r\n    animation-name: bounce;\r\n    -webkit-transform-origin: center bottom;\r\n    transform-origin: center bottom;\r\n}\r\n\r\n\r\n\r\n/*jump animation */\r\n@keyframes jump-1 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    40% {\r\n      -webkit-transform: translate3d(0, 20px, 0);\r\n      transform: translate3d(0, 20px, 0);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n  @keyframes jump-2 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    50% {\r\n      -webkit-transform: translate3d(0, 30px, 0);\r\n      transform: translate3d(0, 30px, 0);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n  @keyframes jump-3 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    40% {\r\n      -webkit-transform: translate3d(0, 50px, 0) scale(.7);\r\n      transform: translate3d(0, 50px, 0) scale(.7);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n  @keyframes jump-4 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    40% {\r\n      -webkit-transform: translate3d(0, 20px, 0) scale(.8);\r\n      transform: translate3d(0, 20px, 0) scale(.8);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n  @keyframes jump-5 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    40% {\r\n      transform: translate3d(0, 10px, 0);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n@-webkit-keyframes flash {\r\n\r\n    from,\r\n    50%,\r\n    to {\r\n        opacity: 1;\r\n    }\r\n\r\n    25%,\r\n    75% {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes flash {\r\n\r\n    from,\r\n    50%,\r\n    to {\r\n        opacity: 1;\r\n    }\r\n\r\n    25%,\r\n    75% {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.flash {\r\n    -webkit-animation-name: flash;\r\n    animation-name: flash;\r\n}\r\n\r\n\r\n@-webkit-keyframes pulse {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: scale3d(1.05, 1.05, 1.05);\r\n        transform: scale3d(1.05, 1.05, 1.05);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n@keyframes pulse {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: scale3d(1.05, 1.05, 1.05);\r\n        transform: scale3d(1.05, 1.05, 1.05);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n.pulse {\r\n    -webkit-animation-name: pulse;\r\n    animation-name: pulse;\r\n}\r\n\r\n@-webkit-keyframes rubberBand {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: scale3d(1.25, 0.75, 1);\r\n        transform: scale3d(1.25, 0.75, 1);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: scale3d(0.75, 1.25, 1);\r\n        transform: scale3d(0.75, 1.25, 1);\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: scale3d(1.15, 0.85, 1);\r\n        transform: scale3d(1.15, 0.85, 1);\r\n    }\r\n\r\n    65% {\r\n        -webkit-transform: scale3d(.95, 1.05, 1);\r\n        transform: scale3d(.95, 1.05, 1);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: scale3d(1.05, .95, 1);\r\n        transform: scale3d(1.05, .95, 1);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n@keyframes rubberBand {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: scale3d(1.25, 0.75, 1);\r\n        transform: scale3d(1.25, 0.75, 1);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: scale3d(0.75, 1.25, 1);\r\n        transform: scale3d(0.75, 1.25, 1);\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: scale3d(1.15, 0.85, 1);\r\n        transform: scale3d(1.15, 0.85, 1);\r\n    }\r\n\r\n    65% {\r\n        -webkit-transform: scale3d(.95, 1.05, 1);\r\n        transform: scale3d(.95, 1.05, 1);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: scale3d(1.05, .95, 1);\r\n        transform: scale3d(1.05, .95, 1);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n.rubberBand {\r\n    -webkit-animation-name: rubberBand;\r\n    animation-name: rubberBand;\r\n}\r\n\r\n@-webkit-keyframes shake {\r\n\r\n    from,\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    10%,\r\n    30%,\r\n    50%,\r\n    70%,\r\n    90% {\r\n        -webkit-transform: translate3d(-10px, 0, 0);\r\n        transform: translate3d(-10px, 0, 0);\r\n    }\r\n\r\n    20%,\r\n    40%,\r\n    60%,\r\n    80% {\r\n        -webkit-transform: translate3d(10px, 0, 0);\r\n        transform: translate3d(10px, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes shake {\r\n\r\n    from,\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    10%,\r\n    30%,\r\n    50%,\r\n    70%,\r\n    90% {\r\n        -webkit-transform: translate3d(-10px, 0, 0);\r\n        transform: translate3d(-10px, 0, 0);\r\n    }\r\n\r\n    20%,\r\n    40%,\r\n    60%,\r\n    80% {\r\n        -webkit-transform: translate3d(10px, 0, 0);\r\n        transform: translate3d(10px, 0, 0);\r\n    }\r\n}\r\n\r\n.shake {\r\n    -webkit-animation-name: shake;\r\n    animation-name: shake;\r\n}\r\n\r\n@-webkit-keyframes swing {\r\n    20% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 15deg);\r\n        transform: rotate3d(0, 0, 1, 15deg);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: rotate3d(0, 0, 1, -10deg);\r\n        transform: rotate3d(0, 0, 1, -10deg);\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 5deg);\r\n        transform: rotate3d(0, 0, 1, 5deg);\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: rotate3d(0, 0, 1, -5deg);\r\n        transform: rotate3d(0, 0, 1, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: rotate3d(0, 0, 1, 0deg);\r\n        transform: rotate3d(0, 0, 1, 0deg);\r\n    }\r\n}\r\n\r\n@keyframes swing {\r\n    20% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 15deg);\r\n        transform: rotate3d(0, 0, 1, 15deg);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: rotate3d(0, 0, 1, -10deg);\r\n        transform: rotate3d(0, 0, 1, -10deg);\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 5deg);\r\n        transform: rotate3d(0, 0, 1, 5deg);\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: rotate3d(0, 0, 1, -5deg);\r\n        transform: rotate3d(0, 0, 1, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: rotate3d(0, 0, 1, 0deg);\r\n        transform: rotate3d(0, 0, 1, 0deg);\r\n    }\r\n}\r\n\r\n.swing {\r\n    -webkit-transform-origin: top center;\r\n    transform-origin: top center;\r\n    -webkit-animation-name: swing;\r\n    animation-name: swing;\r\n}\r\n\r\n@-webkit-keyframes tada {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    10%,\r\n    20% {\r\n        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);\r\n        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    30%,\r\n    50%,\r\n    70%,\r\n    90% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);\r\n        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);\r\n    }\r\n\r\n    40%,\r\n    60%,\r\n    80% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);\r\n        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n@keyframes tada {\r\n    from {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n\r\n    10%,\r\n    20% {\r\n        -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);\r\n        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    30%,\r\n    50%,\r\n    70%,\r\n    90% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);\r\n        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);\r\n    }\r\n\r\n    40%,\r\n    60%,\r\n    80% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);\r\n        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n.tada {\r\n    -webkit-animation-name: tada;\r\n    animation-name: tada;\r\n}\r\n\r\n/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */\r\n\r\n@-webkit-keyframes wobble {\r\n    from {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n\r\n    15% {\r\n        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\r\n        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\r\n        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\r\n    }\r\n\r\n    45% {\r\n        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\r\n        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\r\n        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\r\n        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes wobble {\r\n    from {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n\r\n    15% {\r\n        -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\r\n        transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\r\n        transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\r\n    }\r\n\r\n    45% {\r\n        -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\r\n        transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\r\n        transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\r\n        transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.wobble {\r\n    -webkit-animation-name: wobble;\r\n    animation-name: wobble;\r\n}\r\n\r\n@-webkit-keyframes jello {\r\n\r\n    from,\r\n    11.1%,\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n\r\n    22.2% {\r\n        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);\r\n        transform: skewX(-12.5deg) skewY(-12.5deg);\r\n    }\r\n\r\n    33.3% {\r\n        -webkit-transform: skewX(6.25deg) skewY(6.25deg);\r\n        transform: skewX(6.25deg) skewY(6.25deg);\r\n    }\r\n\r\n    44.4% {\r\n        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);\r\n        transform: skewX(-3.125deg) skewY(-3.125deg);\r\n    }\r\n\r\n    55.5% {\r\n        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);\r\n        transform: skewX(1.5625deg) skewY(1.5625deg);\r\n    }\r\n\r\n    66.6% {\r\n        -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);\r\n        transform: skewX(-0.78125deg) skewY(-0.78125deg);\r\n    }\r\n\r\n    77.7% {\r\n        -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);\r\n        transform: skewX(0.390625deg) skewY(0.390625deg);\r\n    }\r\n\r\n    88.8% {\r\n        -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);\r\n        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);\r\n    }\r\n}\r\n\r\n@keyframes jello {\r\n\r\n    from,\r\n    11.1%,\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n\r\n    22.2% {\r\n        -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);\r\n        transform: skewX(-12.5deg) skewY(-12.5deg);\r\n    }\r\n\r\n    33.3% {\r\n        -webkit-transform: skewX(6.25deg) skewY(6.25deg);\r\n        transform: skewX(6.25deg) skewY(6.25deg);\r\n    }\r\n\r\n    44.4% {\r\n        -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);\r\n        transform: skewX(-3.125deg) skewY(-3.125deg);\r\n    }\r\n\r\n    55.5% {\r\n        -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);\r\n        transform: skewX(1.5625deg) skewY(1.5625deg);\r\n    }\r\n\r\n    66.6% {\r\n        -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);\r\n        transform: skewX(-0.78125deg) skewY(-0.78125deg);\r\n    }\r\n\r\n    77.7% {\r\n        -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);\r\n        transform: skewX(0.390625deg) skewY(0.390625deg);\r\n    }\r\n\r\n    88.8% {\r\n        -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);\r\n        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);\r\n    }\r\n}\r\n\r\n.jello {\r\n    -webkit-animation-name: jello;\r\n    animation-name: jello;\r\n    -webkit-transform-origin: center;\r\n    transform-origin: center;\r\n}\r\n\r\n@-webkit-keyframes bounceIn {\r\n\r\n    from,\r\n    20%,\r\n    40%,\r\n    60%,\r\n    80%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    20% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1);\r\n        transform: scale3d(1.1, 1.1, 1.1);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: scale3d(.9, .9, .9);\r\n        transform: scale3d(.9, .9, .9);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1.03, 1.03, 1.03);\r\n        transform: scale3d(1.03, 1.03, 1.03);\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: scale3d(.97, .97, .97);\r\n        transform: scale3d(.97, .97, .97);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n@keyframes bounceIn {\r\n\r\n    from,\r\n    20%,\r\n    40%,\r\n    60%,\r\n    80%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    20% {\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1);\r\n        transform: scale3d(1.1, 1.1, 1.1);\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: scale3d(.9, .9, .9);\r\n        transform: scale3d(.9, .9, .9);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1.03, 1.03, 1.03);\r\n        transform: scale3d(1.03, 1.03, 1.03);\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: scale3d(.97, .97, .97);\r\n        transform: scale3d(.97, .97, .97);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1, 1, 1);\r\n        transform: scale3d(1, 1, 1);\r\n    }\r\n}\r\n\r\n.bounceIn {\r\n    -webkit-animation-name: bounceIn;\r\n    animation-name: bounceIn;\r\n}\r\n\r\n@-webkit-keyframes bounceInDown {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -3000px, 0);\r\n        transform: translate3d(0, -3000px, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, 25px, 0);\r\n        transform: translate3d(0, 25px, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(0, -10px, 0);\r\n        transform: translate3d(0, -10px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, 5px, 0);\r\n        transform: translate3d(0, 5px, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes bounceInDown {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -3000px, 0);\r\n        transform: translate3d(0, -3000px, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, 25px, 0);\r\n        transform: translate3d(0, 25px, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(0, -10px, 0);\r\n        transform: translate3d(0, -10px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, 5px, 0);\r\n        transform: translate3d(0, 5px, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.bounceInDown {\r\n    -webkit-animation-name: bounceInDown;\r\n    animation-name: bounceInDown;\r\n}\r\n\r\n@-webkit-keyframes bounceInLeft {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-3000px, 0, 0);\r\n        transform: translate3d(-3000px, 0, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(25px, 0, 0);\r\n        transform: translate3d(25px, 0, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(-10px, 0, 0);\r\n        transform: translate3d(-10px, 0, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(5px, 0, 0);\r\n        transform: translate3d(5px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes bounceInLeft {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    0% {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-3000px, 0, 0);\r\n        transform: translate3d(-3000px, 0, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(25px, 0, 0);\r\n        transform: translate3d(25px, 0, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(-10px, 0, 0);\r\n        transform: translate3d(-10px, 0, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(5px, 0, 0);\r\n        transform: translate3d(5px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.bounceInLeft {\r\n    -webkit-animation-name: bounceInLeft;\r\n    animation-name: bounceInLeft;\r\n}\r\n\r\n@-webkit-keyframes bounceInRight {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(3000px, 0, 0);\r\n        transform: translate3d(3000px, 0, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(-25px, 0, 0);\r\n        transform: translate3d(-25px, 0, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(10px, 0, 0);\r\n        transform: translate3d(10px, 0, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(-5px, 0, 0);\r\n        transform: translate3d(-5px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes bounceInRight {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(3000px, 0, 0);\r\n        transform: translate3d(3000px, 0, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(-25px, 0, 0);\r\n        transform: translate3d(-25px, 0, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(10px, 0, 0);\r\n        transform: translate3d(10px, 0, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(-5px, 0, 0);\r\n        transform: translate3d(-5px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.bounceInRight {\r\n    -webkit-animation-name: bounceInRight;\r\n    animation-name: bounceInRight;\r\n}\r\n\r\n@-webkit-keyframes bounceInUp {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 3000px, 0);\r\n        transform: translate3d(0, 3000px, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, -20px, 0);\r\n        transform: translate3d(0, -20px, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(0, 10px, 0);\r\n        transform: translate3d(0, 10px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, -5px, 0);\r\n        transform: translate3d(0, -5px, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounceInUp {\r\n\r\n    from,\r\n    60%,\r\n    75%,\r\n    90%,\r\n    to {\r\n        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    }\r\n\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 3000px, 0);\r\n        transform: translate3d(0, 3000px, 0);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, -20px, 0);\r\n        transform: translate3d(0, -20px, 0);\r\n    }\r\n\r\n    75% {\r\n        -webkit-transform: translate3d(0, 10px, 0);\r\n        transform: translate3d(0, 10px, 0);\r\n    }\r\n\r\n    90% {\r\n        -webkit-transform: translate3d(0, -5px, 0);\r\n        transform: translate3d(0, -5px, 0);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n.bounceInUp {\r\n    -webkit-animation-name: bounceInUp;\r\n    animation-name: bounceInUp;\r\n}\r\n\r\n@-webkit-keyframes bounceOut {\r\n    20% {\r\n        -webkit-transform: scale3d(.9, .9, .9);\r\n        transform: scale3d(.9, .9, .9);\r\n    }\r\n\r\n    50%,\r\n    55% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1);\r\n        transform: scale3d(1.1, 1.1, 1.1);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n}\r\n\r\n@keyframes bounceOut {\r\n    20% {\r\n        -webkit-transform: scale3d(.9, .9, .9);\r\n        transform: scale3d(.9, .9, .9);\r\n    }\r\n\r\n    50%,\r\n    55% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(1.1, 1.1, 1.1);\r\n        transform: scale3d(1.1, 1.1, 1.1);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n}\r\n\r\n.bounceOut {\r\n    -webkit-animation-name: bounceOut;\r\n    animation-name: bounceOut;\r\n}\r\n\r\n@-webkit-keyframes bounceOutDown {\r\n    20% {\r\n        -webkit-transform: translate3d(0, 10px, 0);\r\n        transform: translate3d(0, 10px, 0);\r\n    }\r\n\r\n    40%,\r\n    45% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, -20px, 0);\r\n        transform: translate3d(0, -20px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounceOutDown {\r\n    20% {\r\n        -webkit-transform: translate3d(0, 10px, 0);\r\n        transform: translate3d(0, 10px, 0);\r\n    }\r\n\r\n    40%,\r\n    45% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, -20px, 0);\r\n        transform: translate3d(0, -20px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n}\r\n\r\n.bounceOutDown {\r\n    -webkit-animation-name: bounceOutDown;\r\n    animation-name: bounceOutDown;\r\n}\r\n\r\n@-webkit-keyframes bounceOutLeft {\r\n    20% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(20px, 0, 0);\r\n        transform: translate3d(20px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounceOutLeft {\r\n    20% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(20px, 0, 0);\r\n        transform: translate3d(20px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n}\r\n\r\n.bounceOutLeft {\r\n    -webkit-animation-name: bounceOutLeft;\r\n    animation-name: bounceOutLeft;\r\n}\r\n\r\n@-webkit-keyframes bounceOutRight {\r\n    20% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(-20px, 0, 0);\r\n        transform: translate3d(-20px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounceOutRight {\r\n    20% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(-20px, 0, 0);\r\n        transform: translate3d(-20px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n}\r\n\r\n.bounceOutRight {\r\n    -webkit-animation-name: bounceOutRight;\r\n    animation-name: bounceOutRight;\r\n}\r\n\r\n@-webkit-keyframes bounceOutUp {\r\n    20% {\r\n        -webkit-transform: translate3d(0, -10px, 0);\r\n        transform: translate3d(0, -10px, 0);\r\n    }\r\n\r\n    40%,\r\n    45% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, 20px, 0);\r\n        transform: translate3d(0, 20px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n}\r\n\r\n@keyframes bounceOutUp {\r\n    20% {\r\n        -webkit-transform: translate3d(0, -10px, 0);\r\n        transform: translate3d(0, -10px, 0);\r\n    }\r\n\r\n    40%,\r\n    45% {\r\n        opacity: 1;\r\n        -webkit-transform: translate3d(0, 20px, 0);\r\n        transform: translate3d(0, 20px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n}\r\n\r\n.bounceOutUp {\r\n    -webkit-animation-name: bounceOutUp;\r\n    animation-name: bounceOutUp;\r\n}\r\n\r\n@-webkit-keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.fadeIn {\r\n    -webkit-animation-name: fadeIn;\r\n    animation-name: fadeIn;\r\n}\r\n\r\n@-webkit-keyframes fadeInDown {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInDown {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInDown {\r\n    -webkit-animation-name: fadeInDown;\r\n    animation-name: fadeInDown;\r\n}\r\n\r\n@-webkit-keyframes fadeInDownBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInDownBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInDownBig {\r\n    -webkit-animation-name: fadeInDownBig;\r\n    animation-name: fadeInDownBig;\r\n}\r\n\r\n@-webkit-keyframes fadeInLeft {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInLeft {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInLeft {\r\n    -webkit-animation-name: fadeInLeft;\r\n    animation-name: fadeInLeft;\r\n}\r\n\r\n@-webkit-keyframes fadeInLeftBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInLeftBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInLeftBig {\r\n    -webkit-animation-name: fadeInLeftBig;\r\n    animation-name: fadeInLeftBig;\r\n}\r\n\r\n@-webkit-keyframes fadeInRight {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInRight {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInRight {\r\n    -webkit-animation-name: fadeInRight;\r\n    animation-name: fadeInRight;\r\n}\r\n\r\n@-webkit-keyframes fadeInRightBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInRightBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInRightBig {\r\n    -webkit-animation-name: fadeInRightBig;\r\n    animation-name: fadeInRightBig;\r\n}\r\n\r\n@-webkit-keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n@keyframes fadeInUp2 {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 40%, 0);\r\n        transform: translate3d(0, 40%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n\r\n.fadeInUp {\r\n    -webkit-animation-name: fadeInUp;\r\n    animation-name: fadeInUp;\r\n}\r\n\r\n@-webkit-keyframes fadeInUpBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes fadeInUpBig {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.fadeInUpBig {\r\n    -webkit-animation-name: fadeInUpBig;\r\n    animation-name: fadeInUpBig;\r\n}\r\n\r\n@-webkit-keyframes fadeOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes fadeOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.fadeOut {\r\n    -webkit-animation-name: fadeOut;\r\n    animation-name: fadeOut;\r\n}\r\n\r\n@-webkit-keyframes fadeOutDown {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutDown {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n}\r\n\r\n.fadeOutDown {\r\n    -webkit-animation-name: fadeOutDown;\r\n    animation-name: fadeOutDown;\r\n}\r\n\r\n@-webkit-keyframes fadeOutDownBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutDownBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 2000px, 0);\r\n        transform: translate3d(0, 2000px, 0);\r\n    }\r\n}\r\n\r\n.fadeOutDownBig {\r\n    -webkit-animation-name: fadeOutDownBig;\r\n    animation-name: fadeOutDownBig;\r\n}\r\n\r\n@-webkit-keyframes fadeOutLeft {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutLeft {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n}\r\n\r\n.fadeOutLeft {\r\n    -webkit-animation-name: fadeOutLeft;\r\n    animation-name: fadeOutLeft;\r\n}\r\n\r\n@-webkit-keyframes fadeOutLeftBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutLeftBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-2000px, 0, 0);\r\n        transform: translate3d(-2000px, 0, 0);\r\n    }\r\n}\r\n\r\n.fadeOutLeftBig {\r\n    -webkit-animation-name: fadeOutLeftBig;\r\n    animation-name: fadeOutLeftBig;\r\n}\r\n\r\n@-webkit-keyframes fadeOutRight {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutRight {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n}\r\n\r\n.fadeOutRight {\r\n    -webkit-animation-name: fadeOutRight;\r\n    animation-name: fadeOutRight;\r\n}\r\n\r\n@-webkit-keyframes fadeOutRightBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutRightBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(2000px, 0, 0);\r\n        transform: translate3d(2000px, 0, 0);\r\n    }\r\n}\r\n\r\n.fadeOutRightBig {\r\n    -webkit-animation-name: fadeOutRightBig;\r\n    animation-name: fadeOutRightBig;\r\n}\r\n\r\n@-webkit-keyframes fadeOutUp {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutUp {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n}\r\n\r\n.fadeOutUp {\r\n    -webkit-animation-name: fadeOutUp;\r\n    animation-name: fadeOutUp;\r\n}\r\n\r\n@-webkit-keyframes fadeOutUpBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n}\r\n\r\n@keyframes fadeOutUpBig {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, -2000px, 0);\r\n        transform: translate3d(0, -2000px, 0);\r\n    }\r\n}\r\n\r\n.fadeOutUpBig {\r\n    -webkit-animation-name: fadeOutUpBig;\r\n    animation-name: fadeOutUpBig;\r\n}\r\n\r\n@-webkit-keyframes flip {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);\r\n        -webkit-animation-timing-function: ease-out;\r\n        animation-timing-function: ease-out;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);\r\n        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);\r\n        -webkit-animation-timing-function: ease-out;\r\n        animation-timing-function: ease-out;\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);\r\n        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);\r\n        transform: perspective(400px) scale3d(.95, .95, .95);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n}\r\n\r\n@keyframes flip {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -360deg);\r\n        -webkit-animation-timing-function: ease-out;\r\n        animation-timing-function: ease-out;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);\r\n        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);\r\n        -webkit-animation-timing-function: ease-out;\r\n        animation-timing-function: ease-out;\r\n    }\r\n\r\n    50% {\r\n        -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);\r\n        transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) scale3d(.95, .95, .95);\r\n        transform: perspective(400px) scale3d(.95, .95, .95);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n}\r\n\r\n.animated.flip {\r\n    -webkit-backface-visibility: visible;\r\n    backface-visibility: visible;\r\n    -webkit-animation-name: flip;\r\n    animation-name: flip;\r\n}\r\n\r\n@-webkit-keyframes flipInX {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n        opacity: 0;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n}\r\n\r\n@keyframes flipInX {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n        opacity: 0;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n}\r\n\r\n.flipInX {\r\n    -webkit-backface-visibility: visible !important;\r\n    backface-visibility: visible !important;\r\n    -webkit-animation-name: flipInX;\r\n    animation-name: flipInX;\r\n}\r\n\r\n@-webkit-keyframes flipInY {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n        opacity: 0;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n}\r\n\r\n@keyframes flipInY {\r\n    from {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n        opacity: 0;\r\n    }\r\n\r\n    40% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);\r\n        -webkit-animation-timing-function: ease-in;\r\n        animation-timing-function: ease-in;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -5deg);\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n}\r\n\r\n.flipInY {\r\n    -webkit-backface-visibility: visible !important;\r\n    backface-visibility: visible !important;\r\n    -webkit-animation-name: flipInY;\r\n    animation-name: flipInY;\r\n}\r\n\r\n@-webkit-keyframes flipOutX {\r\n    from {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes flipOutX {\r\n    from {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.flipOutX {\r\n    -webkit-animation-name: flipOutX;\r\n    animation-name: flipOutX;\r\n    -webkit-backface-visibility: visible !important;\r\n    backface-visibility: visible !important;\r\n}\r\n\r\n@-webkit-keyframes flipOutY {\r\n    from {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes flipOutY {\r\n    from {\r\n        -webkit-transform: perspective(400px);\r\n        transform: perspective(400px);\r\n    }\r\n\r\n    30% {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, -15deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.flipOutY {\r\n    -webkit-backface-visibility: visible !important;\r\n    backface-visibility: visible !important;\r\n    -webkit-animation-name: flipOutY;\r\n    animation-name: flipOutY;\r\n}\r\n\r\n@-webkit-keyframes lightSpeedIn {\r\n    from {\r\n        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);\r\n        transform: translate3d(100%, 0, 0) skewX(-30deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: skewX(20deg);\r\n        transform: skewX(20deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: skewX(-5deg);\r\n        transform: skewX(-5deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes lightSpeedIn {\r\n    from {\r\n        -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);\r\n        transform: translate3d(100%, 0, 0) skewX(-30deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    60% {\r\n        -webkit-transform: skewX(20deg);\r\n        transform: skewX(20deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    80% {\r\n        -webkit-transform: skewX(-5deg);\r\n        transform: skewX(-5deg);\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.lightSpeedIn {\r\n    -webkit-animation-name: lightSpeedIn;\r\n    animation-name: lightSpeedIn;\r\n    -webkit-animation-timing-function: ease-out;\r\n    animation-timing-function: ease-out;\r\n}\r\n\r\n@-webkit-keyframes lightSpeedOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);\r\n        transform: translate3d(100%, 0, 0) skewX(30deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes lightSpeedOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);\r\n        transform: translate3d(100%, 0, 0) skewX(30deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.lightSpeedOut {\r\n    -webkit-animation-name: lightSpeedOut;\r\n    animation-name: lightSpeedOut;\r\n    -webkit-animation-timing-function: ease-in;\r\n    animation-timing-function: ease-in;\r\n}\r\n\r\n@-webkit-keyframes rotateIn {\r\n    from {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: rotate3d(0, 0, 1, -200deg);\r\n        transform: rotate3d(0, 0, 1, -200deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes rotateIn {\r\n    from {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: rotate3d(0, 0, 1, -200deg);\r\n        transform: rotate3d(0, 0, 1, -200deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.rotateIn {\r\n    -webkit-animation-name: rotateIn;\r\n    animation-name: rotateIn;\r\n}\r\n\r\n@-webkit-keyframes rotateInDownLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes rotateInDownLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.rotateInDownLeft {\r\n    -webkit-animation-name: rotateInDownLeft;\r\n    animation-name: rotateInDownLeft;\r\n}\r\n\r\n@-webkit-keyframes rotateInDownRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes rotateInDownRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.rotateInDownRight {\r\n    -webkit-animation-name: rotateInDownRight;\r\n    animation-name: rotateInDownRight;\r\n}\r\n\r\n@-webkit-keyframes rotateInUpLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes rotateInUpLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.rotateInUpLeft {\r\n    -webkit-animation-name: rotateInUpLeft;\r\n    animation-name: rotateInUpLeft;\r\n}\r\n\r\n@-webkit-keyframes rotateInUpRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -90deg);\r\n        transform: rotate3d(0, 0, 1, -90deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes rotateInUpRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -90deg);\r\n        transform: rotate3d(0, 0, 1, -90deg);\r\n        opacity: 0;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.rotateInUpRight {\r\n    -webkit-animation-name: rotateInUpRight;\r\n    animation-name: rotateInUpRight;\r\n}\r\n\r\n@-webkit-keyframes rotateOut {\r\n    from {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: rotate3d(0, 0, 1, 200deg);\r\n        transform: rotate3d(0, 0, 1, 200deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes rotateOut {\r\n    from {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: center;\r\n        transform-origin: center;\r\n        -webkit-transform: rotate3d(0, 0, 1, 200deg);\r\n        transform: rotate3d(0, 0, 1, 200deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.rotateOut {\r\n    -webkit-animation-name: rotateOut;\r\n    animation-name: rotateOut;\r\n}\r\n\r\n@-webkit-keyframes rotateOutDownLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes rotateOutDownLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 45deg);\r\n        transform: rotate3d(0, 0, 1, 45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.rotateOutDownLeft {\r\n    -webkit-animation-name: rotateOutDownLeft;\r\n    animation-name: rotateOutDownLeft;\r\n}\r\n\r\n@-webkit-keyframes rotateOutDownRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes rotateOutDownRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.rotateOutDownRight {\r\n    -webkit-animation-name: rotateOutDownRight;\r\n    animation-name: rotateOutDownRight;\r\n}\r\n\r\n@-webkit-keyframes rotateOutUpLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes rotateOutUpLeft {\r\n    from {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: left bottom;\r\n        transform-origin: left bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, -45deg);\r\n        transform: rotate3d(0, 0, 1, -45deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.rotateOutUpLeft {\r\n    -webkit-animation-name: rotateOutUpLeft;\r\n    animation-name: rotateOutUpLeft;\r\n}\r\n\r\n@-webkit-keyframes rotateOutUpRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 90deg);\r\n        transform: rotate3d(0, 0, 1, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes rotateOutUpRight {\r\n    from {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform-origin: right bottom;\r\n        transform-origin: right bottom;\r\n        -webkit-transform: rotate3d(0, 0, 1, 90deg);\r\n        transform: rotate3d(0, 0, 1, 90deg);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.rotateOutUpRight {\r\n    -webkit-animation-name: rotateOutUpRight;\r\n    animation-name: rotateOutUpRight;\r\n}\r\n\r\n@-webkit-keyframes hinge {\r\n    0% {\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n    }\r\n\r\n    20%,\r\n    60% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 80deg);\r\n        transform: rotate3d(0, 0, 1, 80deg);\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n    }\r\n\r\n    40%,\r\n    80% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 60deg);\r\n        transform: rotate3d(0, 0, 1, 60deg);\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 700px, 0);\r\n        transform: translate3d(0, 700px, 0);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes hinge {\r\n    0% {\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n    }\r\n\r\n    20%,\r\n    60% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 80deg);\r\n        transform: rotate3d(0, 0, 1, 80deg);\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n    }\r\n\r\n    40%,\r\n    80% {\r\n        -webkit-transform: rotate3d(0, 0, 1, 60deg);\r\n        transform: rotate3d(0, 0, 1, 60deg);\r\n        -webkit-transform-origin: top left;\r\n        transform-origin: top left;\r\n        -webkit-animation-timing-function: ease-in-out;\r\n        animation-timing-function: ease-in-out;\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 700px, 0);\r\n        transform: translate3d(0, 700px, 0);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.hinge {\r\n    -webkit-animation-name: hinge;\r\n    animation-name: hinge;\r\n}\r\n\r\n/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */\r\n\r\n@-webkit-keyframes rollIn {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);\r\n        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes rollIn {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);\r\n        transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.rollIn {\r\n    -webkit-animation-name: rollIn;\r\n    animation-name: rollIn;\r\n}\r\n\r\n/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */\r\n\r\n@-webkit-keyframes rollOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);\r\n        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);\r\n    }\r\n}\r\n\r\n@keyframes rollOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);\r\n        transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);\r\n    }\r\n}\r\n\r\n.rollOut {\r\n    -webkit-animation-name: rollOut;\r\n    animation-name: rollOut;\r\n}\r\n\r\n@-webkit-keyframes zoomIn {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    50% {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes zoomIn {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    50% {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.zoomIn {\r\n    -webkit-animation-name: zoomIn;\r\n    animation-name: zoomIn;\r\n}\r\n\r\n@-webkit-keyframes zoomInDown {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomInDown {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomInDown {\r\n    -webkit-animation-name: zoomInDown;\r\n    animation-name: zoomInDown;\r\n}\r\n\r\n@-webkit-keyframes zoomInLeft {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomInLeft {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(-1000px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(10px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomInLeft {\r\n    -webkit-animation-name: zoomInLeft;\r\n    animation-name: zoomInLeft;\r\n}\r\n\r\n@-webkit-keyframes zoomInRight {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomInRight {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(1000px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(-10px, 0, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomInRight {\r\n    -webkit-animation-name: zoomInRight;\r\n    animation-name: zoomInRight;\r\n}\r\n\r\n@-webkit-keyframes zoomInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, 1000px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    60% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomInUp {\r\n    -webkit-animation-name: zoomInUp;\r\n    animation-name: zoomInUp;\r\n}\r\n\r\n@-webkit-keyframes zoomOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    50% {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes zoomOut {\r\n    from {\r\n        opacity: 1;\r\n    }\r\n\r\n    50% {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.3, .3, .3);\r\n        transform: scale3d(.3, .3, .3);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n.zoomOut {\r\n    -webkit-animation-name: zoomOut;\r\n    animation-name: zoomOut;\r\n}\r\n\r\n@-webkit-keyframes zoomOutDown {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);\r\n        -webkit-transform-origin: center bottom;\r\n        transform-origin: center bottom;\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomOutDown {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);\r\n        -webkit-transform-origin: center bottom;\r\n        transform-origin: center bottom;\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomOutDown {\r\n    -webkit-animation-name: zoomOutDown;\r\n    animation-name: zoomOutDown;\r\n}\r\n\r\n@-webkit-keyframes zoomOutLeft {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);\r\n        transform: scale(.1) translate3d(-2000px, 0, 0);\r\n        -webkit-transform-origin: left center;\r\n        transform-origin: left center;\r\n    }\r\n}\r\n\r\n@keyframes zoomOutLeft {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(42px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale(.1) translate3d(-2000px, 0, 0);\r\n        transform: scale(.1) translate3d(-2000px, 0, 0);\r\n        -webkit-transform-origin: left center;\r\n        transform-origin: left center;\r\n    }\r\n}\r\n\r\n.zoomOutLeft {\r\n    -webkit-animation-name: zoomOutLeft;\r\n    animation-name: zoomOutLeft;\r\n}\r\n\r\n@-webkit-keyframes zoomOutRight {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);\r\n        transform: scale(.1) translate3d(2000px, 0, 0);\r\n        -webkit-transform-origin: right center;\r\n        transform-origin: right center;\r\n    }\r\n}\r\n\r\n@keyframes zoomOutRight {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(-42px, 0, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale(.1) translate3d(2000px, 0, 0);\r\n        transform: scale(.1) translate3d(2000px, 0, 0);\r\n        -webkit-transform-origin: right center;\r\n        transform-origin: right center;\r\n    }\r\n}\r\n\r\n.zoomOutRight {\r\n    -webkit-animation-name: zoomOutRight;\r\n    animation-name: zoomOutRight;\r\n}\r\n\r\n@-webkit-keyframes zoomOutUp {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);\r\n        -webkit-transform-origin: center bottom;\r\n        transform-origin: center bottom;\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n@keyframes zoomOutUp {\r\n    40% {\r\n        opacity: 1;\r\n        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);\r\n        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);\r\n    }\r\n\r\n    to {\r\n        opacity: 0;\r\n        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);\r\n        transform: scale3d(.1, .1, .1) translate3d(0, -2000px, 0);\r\n        -webkit-transform-origin: center bottom;\r\n        transform-origin: center bottom;\r\n        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);\r\n    }\r\n}\r\n\r\n.zoomOutUp {\r\n    -webkit-animation-name: zoomOutUp;\r\n    animation-name: zoomOutUp;\r\n}\r\n\r\n@-webkit-keyframes slideInDown {\r\n    from {\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideInDown {\r\n    from {\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n.slideInDown {\r\n    -webkit-animation-name: slideInDown;\r\n    animation-name: slideInDown;\r\n}\r\n\r\n@-webkit-keyframes slideInLeft {\r\n    from {\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideInLeft {\r\n    from {\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n@keyframes slideInLeft2 {\r\n    from {\r\n        -webkit-transform: translate3d(-10%, 0, 0);\r\n        transform: translate3d(-10%, 0, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n.slideInLeft {\r\n    -webkit-animation-name: slideInLeft;\r\n    animation-name: slideInLeft;\r\n}\r\n\r\n@-webkit-keyframes slideInRight {\r\n    from {\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideInRight {\r\n    from {\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n.slideInRight {\r\n    -webkit-animation-name: slideInRight;\r\n    animation-name: slideInRight;\r\n}\r\n\r\n@-webkit-keyframes slideInUp {\r\n    from {\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideInUp {\r\n    from {\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n        visibility: visible;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n@keyframes slideInUp2 {\r\n    from {\r\n        -webkit-transform: translate3d(0, 20%, 0);\r\n        transform: translate3d(0, 20%, 0);\r\n        visibility: hidden;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n        visibility: visible;\r\n    }\r\n}\r\n@keyframes slideInUp3 {\r\n    from {\r\n        -webkit-transform: translate3d(0, 20px, 0);\r\n        transform: translate3d(0, 20px, 0);\r\n        visibility: hidden;\r\n    }\r\n\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n        visibility: visible;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n[data-aos=\"slideInUp2\"] {\r\n    opacity: 0;\r\n    transition-property: transform, opacity;\r\n  \r\n    &.aos-animate {\r\n      opacity: 1;\r\n    }\r\n  \r\n    @media screen and (min-width: 768px) {\r\n      transform: translateY(30px);\r\n  \r\n      &.aos-animate {\r\n        transform: translateY(0);\r\n      }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.slideInUp {\r\n    -webkit-animation-name: slideInUp;\r\n    animation-name: slideInUp;\r\n}\r\n\r\n@-webkit-keyframes slideOutDown {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideOutDown {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(0, 100%, 0);\r\n        transform: translate3d(0, 100%, 0);\r\n    }\r\n}\r\n\r\n.slideOutDown {\r\n    -webkit-animation-name: slideOutDown;\r\n    animation-name: slideOutDown;\r\n}\r\n\r\n@-webkit-keyframes slideOutLeft {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideOutLeft {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(-100%, 0, 0);\r\n        transform: translate3d(-100%, 0, 0);\r\n    }\r\n}\r\n\r\n.slideOutLeft {\r\n    -webkit-animation-name: slideOutLeft;\r\n    animation-name: slideOutLeft;\r\n}\r\n\r\n@-webkit-keyframes slideOutRight {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideOutRight {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(100%, 0, 0);\r\n        transform: translate3d(100%, 0, 0);\r\n    }\r\n}\r\n\r\n.slideOutRight {\r\n    -webkit-animation-name: slideOutRight;\r\n    animation-name: slideOutRight;\r\n}\r\n\r\n@-webkit-keyframes slideOutUp {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n}\r\n\r\n@keyframes slideOutUp {\r\n    from {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n\r\n    to {\r\n        visibility: hidden;\r\n        -webkit-transform: translate3d(0, -100%, 0);\r\n        transform: translate3d(0, -100%, 0);\r\n    }\r\n}\r\n\r\n.slideOutUp {\r\n    -webkit-animation-name: slideOutUp;\r\n    animation-name: slideOutUp;\r\n}\r\n\r\n\r\n@keyframes jump-1 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    40% {\r\n      -webkit-transform: translate3d(0, 20px, 0);\r\n      transform: translate3d(0, 20px, 0);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n@keyframes jump-2 {\r\n    0% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n    50% {\r\n      -webkit-transform: translate3d(0, 30px, 0);\r\n      transform: translate3d(0, 30px, 0);\r\n    }\r\n    100% {\r\n      -webkit-transform: translate3d(0, 0, 0);\r\n      transform: translate3d(0, 0, 0);\r\n    }\r\n  }\r\n\r\n@keyframes rotateIt {\r\n    to {\r\n      transform: rotate(-360deg);\r\n    }\r\n\r\n}\r\n@keyframes rotateIt2 {\r\n    to {\r\n      transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n\r\n@keyframes shape-service-1{\r\n    0% {\r\n        right: -40%;\r\n        top: 30%;\r\n    }\r\n    100% {\r\n        right: -23%;\r\n        top: 0;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n@keyframes animate-floting {\r\n    0% {\r\n      transform: translateX(50%);\r\n    }\r\n    50%{\r\n        transform: translateX(-40%);\r\n    }\r\n    100% {\r\n        transform: translateX(40%);\r\n    }\r\n}\r\n@keyframes animate-floting-2 {\r\n    0% {\r\n        transform: translateX(-50%);\r\n      }\r\n      50%{\r\n          transform: translateX(40%);\r\n      }\r\n      100% {\r\n          transform: translateX(-40%);\r\n      }\r\n}\r\n\r\n@keyframes animate-floting-3{\r\n    0% {\r\n        transform: translateX(-20%);\r\n      }\r\n      50%{\r\n          transform: translateX(0%);\r\n      }\r\n      100% {\r\n          transform: translateX(-20%);\r\n      }\r\n}\r\n\r\n\r\n.floting-line{\r\n    animation: animate-floting 15s linear infinite;\r\n    &:hover{\r\n        animation-play-state: paused;\r\n    }\r\n}\r\n.floting-line-2{\r\n    animation: animate-floting-2 15s linear infinite;\r\n    &:hover{\r\n        animation-play-state: paused;\r\n    }\r\n}\r\n\r\n@keyframes waves {\r\n    0% {\r\n      -webkit-transform: scale(0.2, 0.2);\r\n      transform: scale(0.2, 0.2);\r\n      opacity: 0;\r\n      -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)\";\r\n    }\r\n    50% {\r\n      opacity: 0.9;\r\n      -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=90)\";\r\n    }\r\n    100% {\r\n      -webkit-transform: scale(0.9, 0.9);\r\n      transform: scale(0.9, 0.9);\r\n      opacity: 0;\r\n      -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)\";\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n@keyframes vsmorph {\r\n    0% {\r\n        border-radius: var(--morp-value);\r\n    }\r\n    50% {\r\n        border-radius: var(--morp-md-value);\r\n    }\r\n    100% {\r\n        border-radius: 40% 60%;\r\n    }\r\n}\r\n@keyframes morpspin {\r\n    to {\r\n        -webkit-transform: rotate(1turn);\r\n        transform: rotate(1turn);\r\n    }\r\n}\r\n\r\n\r\n\r\n", "// text blinking start form hear\r\n@keyframes customOne {\r\n    0% {\r\n        -webkit-transform: translateY(-50%) scale(0);\r\n        transform: translateY(-50%) scale(0);\r\n        opacity: 1\r\n    }\r\n\r\n    100% {\r\n        -webkit-transform: translateY(-50%) scale(1.3);\r\n        transform: translateY(-50%) scale(1.3);\r\n        opacity: 0\r\n    }\r\n}\r\n\r\n@keyframes liveAuction {\r\n    0% {\r\n        background: var(--color-white);\r\n    }\r\n\r\n    100% {\r\n        background: var(--color-danger);\r\n    }\r\n}\r\n\r\n\r\n.cd-intro {\r\n    margin: 4em auto;\r\n}\r\n\r\n@media only screen and (min-width: 768px) {\r\n    .cd-intro {\r\n        margin: 5em auto;\r\n    }\r\n}\r\n\r\n@media only screen and (min-width: 1170px) {\r\n    .cd-intro {\r\n        margin: 6em auto;\r\n    }\r\n}\r\n\r\n.cd-headline {\r\n    font-size: 3rem;\r\n    line-height: 1.2;\r\n}\r\n\r\n@media only screen and (min-width: 768px) {\r\n    .cd-headline {\r\n        font-size: 4.4rem;\r\n        font-weight: 300;\r\n    }\r\n}\r\n\r\n@media only screen and (min-width: 1170px) {\r\n    .cd-headline {\r\n        font-size: 48px;\r\n    }\r\n}\r\n\r\n@media only screen and (max-width: 768px) {\r\n    .cd-headline {\r\n        font-size: 40px;\r\n    }\r\n}\r\n\r\n@media only screen and (max-width: 479px) {\r\n    .cd-headline {\r\n        font-size: 26px;\r\n    }\r\n}\r\n\r\n.cd-words-wrapper {\r\n    display: inline-block;\r\n    position: relative;\r\n    text-align: left;\r\n}\r\n\r\n.cd-words-wrapper b {\r\n    display: inline-block;\r\n    position: absolute;\r\n    white-space: nowrap;\r\n    left: 0;\r\n    top: 0;\r\n}\r\n\r\n.cd-words-wrapper b.is-visible {\r\n    position: relative;\r\n}\r\n\r\n.no-js .cd-words-wrapper b {\r\n    opacity: 0;\r\n}\r\n\r\n.no-js .cd-words-wrapper b.is-visible {\r\n    opacity: 1;\r\n}\r\n\r\n/* -------------------------------- \r\n\r\nxclip \r\n\r\n-------------------------------- */\r\n\r\n.cd-headline.clip span {\r\n    display: inline-block;\r\n    padding: 0;\r\n}\r\n\r\n.cd-headline.clip .cd-words-wrapper {\r\n    overflow: hidden;\r\n    vertical-align: middle;\r\n    position: relative;\r\n    margin-top: -20px;\r\n    b{\r\n        font-weight: 700;\r\n    }\r\n}\r\n\r\n.cd-headline.clip .cd-words-wrapper::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 50%;\r\n    right: 0;\r\n    width: 4px;\r\n    height: 100%;\r\n    background-color: var(--color-primary-3);\r\n    transform: translateY(-50%);\r\n}\r\n\r\n.cd-headline.clip b {\r\n    opacity: 0;\r\n}\r\n\r\n.cd-headline.clip b.is-visible {\r\n    opacity: 1;\r\n}\r\n", "/**\r\n * Settings\r\n */\r\n\r\n $sal-animation-duration: 0.2s !default;\r\n $sal-animation-delay: 0s !default;\r\n $sal-slide-offset: 20% !default;\r\n $sal-zoom-in-scale: 0.5 !default;\r\n $sal-zoom-out-scale: 1.1 !default;\r\n $sal-flip-rotate: 91deg !default;\r\n \r\n /**\r\n   * Easings\r\n   */\r\n \r\n $sal-easings: (linear: linear,\r\n     ease: ease,\r\n     ease-in: ease-in,\r\n     ease-out: ease-out,\r\n     ease-in-out: ease-in-out,\r\n \r\n     ease-in-cubic: cubic-bezier(.55, .055, .675, .19),\r\n     ease-out-cubic: cubic-bezier(.215, .61, .355, 1),\r\n     ease-in-out-cubic: cubic-bezier(.645, .045, .355, 1),\r\n \r\n     ease-in-circ: cubic-bezier(.6, .04, .98, .335),\r\n     ease-out-circ: cubic-bezier(.075, .82, .165, 1),\r\n     ease-in-out-circ: cubic-bezier(.785, .135, .15, .86),\r\n \r\n     ease-in-expo: cubic-bezier(.95, .05, .795, .035),\r\n     ease-out-expo: cubic-bezier(.19, 1, .22, 1),\r\n     ease-in-out-expo: cubic-bezier(1, 0, 0, 1),\r\n \r\n     ease-in-quad: cubic-bezier(.55, .085, .68, .53),\r\n     ease-out-quad: cubic-bezier(.25, .46, .45, .94),\r\n     ease-in-out-quad: cubic-bezier(.455, .03, .515, .955),\r\n \r\n     ease-in-quart: cubic-bezier(.895, .03, .685, .22),\r\n     ease-out-quart: cubic-bezier(.165, .84, .44, 1),\r\n     ease-in-out-quart: cubic-bezier(.77, 0, .175, 1),\r\n \r\n     ease-in-quint: cubic-bezier(.755, .05, .855, .06),\r\n     ease-out-quint: cubic-bezier(.23, 1, .32, 1),\r\n     ease-in-out-quint: cubic-bezier(.86, 0, .07, 1),\r\n \r\n     ease-in-sine: cubic-bezier(.47, 0, .745, .715),\r\n     ease-out-sine: cubic-bezier(.39, .575, .565, 1),\r\n     ease-in-out-sine: cubic-bezier(.445, .05, .55, .95),\r\n \r\n     ease-in-back: cubic-bezier(.6, -.28, .735, .045),\r\n     ease-out-back: cubic-bezier(.175, .885, .32, 1.275),\r\n     ease-in-out-back: cubic-bezier(.68, -.55, .265, 1.55));\r\n \r\n /**\r\n   * Core\r\n   */\r\n \r\n [data-sal] {\r\n     transition-duration: $sal-animation-duration;\r\n     transition-delay: $sal-animation-delay;\r\n     transition-duration: var(--sal-duration, $sal-animation-duration);\r\n     transition-delay: var(--sal-delay, $sal-animation-delay);\r\n     transition-timing-function: var(--sal-easing, map-get($sal-easings, 'ease'));\r\n }\r\n \r\n @for $i from 4 through 40 {\r\n     [data-sal][data-sal-duration='#{$i * 50}'] {\r\n         transition-duration: #{$i * 0.05}s;\r\n     }\r\n }\r\n \r\n @for $i from 1 through 20 {\r\n     [data-sal][data-sal-delay='#{$i * 50}'] {\r\n         transition-delay: #{$i * 0.05}s;\r\n     }\r\n }\r\n \r\n @each $key,\r\n $value in $sal-easings {\r\n     [data-sal][data-sal-easing='#{$key}'] {\r\n         transition-timing-function: $value;\r\n     }\r\n }\r\n \r\n /**\r\n   * Animations\r\n   */\r\n \r\n // Fade\r\n [data-sal|='fade'] {\r\n     opacity: 0;\r\n     transition-property: opacity;\r\n }\r\n \r\n [data-sal|='fade'].sal-animate,\r\n body.sal-disabled [data-sal|='fade'] {\r\n     opacity: 1;\r\n }\r\n \r\n // Slide\r\n [data-sal|='slide'] {\r\n     opacity: 0;\r\n     transition-property: opacity, transform;\r\n }\r\n \r\n [data-sal='slide-up'] {\r\n     transform: translateY($sal-slide-offset);\r\n }\r\n \r\n [data-sal='slide-down'] {\r\n     transform: translateY(-$sal-slide-offset);\r\n }\r\n \r\n [data-sal='slide-left'] {\r\n     transform: translateX($sal-slide-offset);\r\n }\r\n \r\n [data-sal='slide-right'] {\r\n     transform: translateX(-$sal-slide-offset);\r\n }\r\n \r\n [data-sal|='slide'].sal-animate,\r\n body.sal-disabled [data-sal|='slide'] {\r\n     opacity: 1;\r\n     transform: none;\r\n }\r\n \r\n // Zoom\r\n [data-sal|='zoom'] {\r\n     opacity: 0;\r\n     transition-property: opacity, transform;\r\n }\r\n \r\n [data-sal='zoom-in'] {\r\n     transform: scale($sal-zoom-in-scale);\r\n }\r\n \r\n [data-sal='zoom-out'] {\r\n     transform: scale($sal-zoom-out-scale);\r\n }\r\n \r\n [data-sal|='zoom'].sal-animate,\r\n body.sal-disabled [data-sal|='zoom'] {\r\n     opacity: 1;\r\n     transform: none;\r\n }\r\n \r\n // Flip\r\n [data-sal|='flip'] {\r\n     backface-visibility: hidden;\r\n     transition-property: transform;\r\n }\r\n \r\n [data-sal='flip-left'] {\r\n     transform: perspective(2000px) rotateY(-$sal-flip-rotate);\r\n }\r\n \r\n [data-sal='flip-right'] {\r\n     transform: perspective(2000px) rotateY($sal-flip-rotate);\r\n }\r\n \r\n [data-sal='flip-up'] {\r\n     transform: perspective(2000px) rotateX(-$sal-flip-rotate);\r\n }\r\n \r\n [data-sal='flip-down'] {\r\n     transform: perspective(2000px) rotateX($sal-flip-rotate);\r\n }\r\n \r\n [data-sal|='flip'].sal-animate,\r\n body.sal-disabled [data-sal|='flip'] {\r\n     transform: none;\r\n }", "\r\nbody{\r\n    background: #F3F4F6;\r\n}", "\r\n\r\n.sidebar_left{\r\n    height: 100vh;\r\n    padding: 16px 0;\r\n    border-right: 1px solid #E2E2E2;\r\n    width: 273px;\r\n    background: #fff;\r\n    z-index: 999;\r\n    @media #{$md-layout} {\r\n        left: -273px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        left: -273px;\r\n    }\r\n    &.collapsed{\r\n        @media #{$md-layout} {\r\n            left: 0 !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            left: 0 !important;\r\n        }\r\n    }\r\n}\r\n\r\n.sidebar_left{\r\n    position: fixed;\r\n    .logo{\r\n        padding: 10px 26px;\r\n    }\r\n}\r\n\r\n.ekomart_dashboard{\r\n    display: flex;\r\n    align-items: flex-start;\r\n    .right-area-body-content{\r\n        width: calc(100% - 273px);\r\n        margin-left: auto;\r\n        @media #{$md-layout} {\r\n            width: 100%;\r\n            margin-left: 0;\r\n        }\r\n        @media #{$sm-layout} {\r\n            width: 100%;\r\n            margin-left: 0;\r\n        }\r\n        &.collapsed{\r\n            @media #{$md-layout} {\r\n                width: calc(100%) !important;\r\n                margin-left: auto !important;\r\n                margin-right: -273px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                width: calc(100%) !important;\r\n                margin-left: auto !important;\r\n                margin-right: -273px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.rts-side-nav-area-left{\r\n    list-style: none;\r\n    padding-left: 0;\r\n    margin-top: 35px;\r\n    li{\r\n        &.single-menu-item{\r\n            margin: 0;\r\n            a{\r\n                display: flex;\r\n                align-items: center;\r\n                gap: 15px;\r\n                width: 100%;\r\n                position: relative;\r\n                height: 46px;\r\n                padding:  0 27px;\r\n                transition: .3s;\r\n                &.with-plus{\r\n                    &::after{\r\n                        position: absolute;\r\n                        content: '\\f078';\r\n                        font-family: var(--font-three);\r\n                        right: 25px;\r\n                        top: 50%;\r\n                        transform: translateY(-50%);\r\n                        transition: .3s;\r\n                        font-size: 14px;\r\n                    }\r\n                }\r\n                p{\r\n                    transition: .3s;\r\n                    font-weight: 500;\r\n                    color: #2D3B29;\r\n                }\r\n                img{\r\n                    transition: .3s;\r\n                }\r\n                &:hover{\r\n                    background: var(--color-primary);\r\n                    color: #fff;\r\n                    p{\r\n                        color: #fff;\r\n                        margin-bottom: 0;\r\n                    }\r\n                    img{\r\n                        filter: brightness(0) saturate(100%) invert(99%) sepia(1%) saturate(7498%) hue-rotate(306deg) brightness(109%) contrast(100%);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .submenu{\r\n            list-style: none;\r\n            li{\r\n                margin: 0;\r\n            }\r\n            .mobile-menu-link{\r\n                height: 42px;\r\n                font-weight: 500;\r\n                color: #2D3B29;\r\n                padding-left: 45px;\r\n                position: relative;\r\n                &::after{\r\n                    position: absolute;\r\n                    content: '';\r\n                    height: 5px;\r\n                    width: 5px;\r\n                    background: var(--color-primary);\r\n                    left: 25px;\r\n                    border-radius: 50%;\r\n                    transition: .3s;\r\n                }\r\n                &:hover{\r\n                    &::after{\r\n                        background: #fff;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.right-area-body-content{\r\n    height: 100vh;\r\n}\r\n\r\n\r\n// header sction\r\n.action-interactive-area__header{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    @media #{$large-mobile} {\r\n        gap: 5px;\r\n    }\r\n   .single_action__haeader{\r\n        height: 40px;\r\n        width: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        border: 1px solid #E5E4FF;\r\n        transition: .3s;\r\n        cursor: pointer;\r\n        &.active{\r\n            &::after{\r\n                display: none !important;\r\n            }\r\n            &::before{\r\n                display: none !important;\r\n            }\r\n        }\r\n        svg{\r\n            path{\r\n                transition: .3s;\r\n            }\r\n        }\r\n        &:hover{\r\n            background: var(--color-primary);\r\n            svg{\r\n                path{\r\n                    fill: #fff;\r\n                }\r\n            }\r\n        }\r\n        .avatar{\r\n            border: 2px solid var(--color-primary);\r\n            border-radius: 50%;\r\n        }\r\n   } \r\n}\r\n\r\n\r\n\r\n.single_action__haeader.search-action{\r\n    position: relative;\r\n    .search-opoup{\r\n        display: none;\r\n        position: absolute;\r\n        top: 150%;\r\n        inset-inline-end: 0;\r\n        min-width: 375px;\r\n        border-radius: 10px;\r\n        background-color: #fff;\r\n        -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        z-index: 9;\r\n        // -webkit-transition: all 0.3s ease;\r\n        // transition: all 0.3s ease;\r\n        @media #{$large-mobile} {\r\n            inset-inline-end: auto;\r\n        }\r\n        @media #{$small-mobile} {\r\n            min-width: 278px;\r\n            right: -157px;\r\n            left: auto;\r\n            inset-inline-end: auto;\r\n        }\r\n        input{\r\n            width: 100%;\r\n            border: none;\r\n            outline: none;\r\n            background: transparent;\r\n            color: #707070;\r\n            padding: 20px 60px 20px 32px;\r\n            margin: 0;\r\n            font-family: inherit;\r\n            font-size: inherit;\r\n            line-height: inherit;\r\n        }\r\n        i{\r\n            position: absolute;\r\n            inset-inline-end: 32px;\r\n            font-size: 20px;\r\n            top: 50%;\r\n            -webkit-transform: translateY(-50%);\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n}\r\n\r\n.single_action__haeader.notification{\r\n    position: relative;\r\n    svg{\r\n        position: relative;\r\n    }\r\n    .notification_main_wrapper{\r\n        display: none;\r\n        position: absolute;\r\n        top: 128%;\r\n        inset-inline-end: 0;\r\n        min-width: 375px;\r\n        border-radius: 10px;\r\n        background-color: #fff;\r\n        -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        z-index: 9;\r\n        // -webkit-transition: all 0.3s ease;\r\n        // transition: all 0.3s ease;\r\n        @media #{$large-mobile} {\r\n            min-width: 320px;\r\n            inset-inline-end: auto;\r\n        }\r\n        @media #{$small-mobile} {\r\n            right: -109px;\r\n        }\r\n        &::before{\r\n            content: '';\r\n            position: absolute;\r\n            top: -10px;\r\n            inset-inline-end: 10px;\r\n            border-bottom: 10px solid #fff;\r\n            -webkit-border-start: 10px solid transparent;\r\n            border-inline-start: 10px solid transparent;\r\n            -webkit-border-end: 10px solid transparent;\r\n            border-inline-end: 10px solid transparent;\r\n        }\r\n        .title{\r\n            display: -webkit-box;\r\n            display: -ms-flexbox;\r\n            display: flex;\r\n            gap: 10px;\r\n            -webkit-box-pack: center;\r\n            -ms-flex-pack: center;\r\n            justify-content: center;\r\n            font-size: 16px;\r\n            line-height: 22px;\r\n            font-weight: 500;\r\n            margin-bottom: 0;\r\n            padding: 15px 20px;\r\n            margin: 20px 20px 5px;\r\n            border-radius: 8px;\r\n            background-color: #f3f2f7;\r\n            -webkit-transition: color 0.3s ease;\r\n            transition: color 0.3s ease;\r\n            .count{\r\n                background-color: var(--color-primary);\r\n                display: -webkit-box;\r\n                display: -ms-flexbox;\r\n                display: flex;\r\n                -webkit-box-align: center;\r\n                -ms-flex-align: center;\r\n                align-items: center;\r\n                -webkit-box-pack: center;\r\n                -ms-flex-pack: center;\r\n                justify-content: center;\r\n                width: 20px;\r\n                height: 20px;\r\n                font-size: 10px;\r\n                border-radius: 100%;\r\n                color:#ffffff;\r\n                background-color:var(--color-primary);\r\n                @media #{$laptop-device} {\r\n                    right: 50px;\r\n                }\r\n            }\r\n        }\r\n        .notification__content{\r\n            padding: 30px 0;\r\n            margin: 0 20px;\r\n            -webkit-padding-end: 6px;\r\n            padding-inline-end: 6px;\r\n            max-height: 200px;\r\n            overflow-y: auto;\r\n            scrollbar-width: none;\r\n            .notification__items{\r\n                display: -webkit-box;\r\n                display: -ms-flexbox;\r\n                display: flex;\r\n                gap: 20px;\r\n                -webkit-box-orient: vertical;\r\n                -webkit-box-direction: normal;\r\n                -ms-flex-direction: column;\r\n                flex-direction: column;\r\n                padding-left: 0;\r\n                margin: 0;\r\n                .single__items{\r\n                    list-style: none;\r\n                    margin: 0;\r\n                    .single-link{\r\n                        display: -webkit-box;\r\n                        display: -ms-flexbox;\r\n                        display: flex;\r\n                        -webkit-box-align: center;\r\n                        -ms-flex-align: center;\r\n                        align-items: center;\r\n                        gap: 10px;\r\n                        color: var(--body-color);\r\n                        font-size: 14px;\r\n                        line-height: 21px;\r\n                        font-weight: 600;\r\n                        cursor: pointer;\r\n                        display: -webkit-box;\r\n                        display: -ms-flexbox;\r\n                        display: flex;\r\n                        gap: 15px !important;\r\n                        font-size: 16px !important;\r\n                        font-weight: 500 !important;\r\n                        color: var(--dark-color) !important;\r\n                        -webkit-box-align: start !important;\r\n                        -ms-flex-align: start !important;\r\n                        align-items: flex-start !important;\r\n                        .avatar{\r\n                            width: 40px;\r\n                            height: 40px;\r\n                            margin-top: 3px;\r\n                            border-radius: 11%;\r\n                            background-color: #eceaf3;\r\n                            border: none;\r\n                            img{\r\n                                width: 100%;\r\n                                height: 100%;\r\n                                -o-object-fit: cover;\r\n                                object-fit: cover;\r\n                            }\r\n                        }\r\n                        .main-content{\r\n                            display: -webkit-box;\r\n                            display: -ms-flexbox;\r\n                            display: flex;\r\n                            -webkit-box-flex: 1;\r\n                            -ms-flex: 1;\r\n                            flex: 1;\r\n                            gap: 5px;\r\n                            -webkit-box-orient: vertical;\r\n                            -webkit-box-direction: normal;\r\n                            -ms-flex-direction: column;\r\n                            flex-direction: column;\r\n                            -webkit-box-pack: justify;\r\n                            -ms-flex-pack: justify;\r\n                            justify-content: space-between;\r\n                            .name-user{\r\n                                display: -webkit-box;\r\n                                display: -ms-flexbox;\r\n                                display: flex;\r\n                                -webkit-box-pack: justify;\r\n                                -ms-flex-pack: justify;\r\n                                justify-content: space-between;\r\n                                font-size: 15px;\r\n                                line-height: 21px;\r\n                                font-weight: 500;\r\n                                margin-bottom: 0;\r\n                                -webkit-transition: color 0.3s ease;\r\n                                transition: color 0.3s ease;\r\n                                .time-ago{\r\n                                    color: var(--color-primary) !important;\r\n                                    font-size: 12px;\r\n                                }\r\n                            }\r\n                            .disc{\r\n                                display: -webkit-box;\r\n                                display: -ms-flexbox;\r\n                                display: flex;\r\n                                -webkit-box-pack: justify;\r\n                                -ms-flex-pack: justify;\r\n                                justify-content: space-between;\r\n                                font-size: 15px;\r\n                                font-weight: 400;\r\n                                font-size: 14px;\r\n                                .count{\r\n                                    width: 10px;\r\n                                    height: 10px;\r\n                                    background-color: var(--color-primary) !important;\r\n                                    display: -webkit-box;\r\n                                    display: -ms-flexbox;\r\n                                    display: flex;\r\n                                    -webkit-box-align: center;\r\n                                    -ms-flex-align: center;\r\n                                    align-items: center;\r\n                                    -webkit-box-pack: center;\r\n                                    -ms-flex-pack: center;\r\n                                    justify-content: center;\r\n                                    width: 10px;\r\n                                    height: 10px;\r\n                                    border-radius: 100%;\r\n                                    font-size: 10px;\r\n                                    color: #fff;\r\n                                    background: var(--color-danger);\r\n                                    margin-top: 5px;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.single_action__haeader.user_avatar__information{\r\n    position: relative;\r\n    .user_information_main_wrapper{\r\n        margin-top: 10px;\r\n        display: none;\r\n        position: absolute;\r\n        top: 100%;\r\n        inset-inline-end: 0;\r\n        min-width: 375px;\r\n        border-radius: 10px;\r\n        background-color: #fff;\r\n        -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n        z-index: 9;\r\n        // -webkit-transition: all 0.3s ease;\r\n        // transition: all 0.3s ease;\r\n        @media #{$small-mobile} {\r\n            min-width: 290px;\r\n        }\r\n        &.language-area{\r\n            min-width: 170px;\r\n            .select-language-area{\r\n                list-style: none;\r\n                padding: 0;\r\n                margin: 0;\r\n                li{\r\n                    margin: 0;\r\n                    a{\r\n                        display: block;\r\n                        padding: 7px 15px;\r\n                        border-bottom: 1px solid #8c899c15;\r\n                        transition: .3s;\r\n                        &:hover{\r\n                            color: #fff;\r\n                            background: var(--color-primary);\r\n                        }\r\n                    }\r\n                    &:last-child{\r\n                        a{\r\n                            border: none;\r\n                            border-radius: 0 0 10px 10px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &::before{\r\n            content: '';\r\n            position: absolute;\r\n            top: -10px;\r\n            inset-inline-end: 10px;\r\n            border-bottom: 10px solid #fff;\r\n            -webkit-border-start: 10px solid transparent;\r\n            border-inline-start: 10px solid transparent;\r\n            -webkit-border-end: 10px solid transparent;\r\n            border-inline-end: 10px solid transparent;\r\n        }\r\n        .user_header{\r\n            display: -webkit-box;\r\n            display: -ms-flexbox;\r\n            display: flex;\r\n            gap: 15px;\r\n            -webkit-box-align: center;\r\n            -ms-flex-align: center;\r\n            align-items: center;\r\n            padding: 10px 15px;\r\n            margin: 20px 20px 0;\r\n            border-radius: 12px;\r\n            background: #f3f2f7;\r\n            .main-avatar{\r\n                border-radius: 5px;\r\n                display: block;\r\n                img{\r\n                    width: 100%;\r\n                    border-radius: 5px;\r\n                }\r\n            }\r\n            .user_naim-information{\r\n                .title{\r\n                    font-size: 18px;\r\n                    line-height: 22px;\r\n                    font-weight: 500;\r\n                    margin-bottom: 5px;\r\n                }\r\n                .desig{\r\n                    font-size: 14px;\r\n                    line-height: 21px;\r\n                    font-weight: 400;\r\n                    color: #a3a3a3;\r\n                    margin-bottom: 0;\r\n                }\r\n            }\r\n        }\r\n        .user_body_content{\r\n            padding: 30px 0;\r\n            margin: 0 20px;\r\n            -webkit-padding-end: 6px;\r\n            padding-inline-end: 6px;\r\n            max-height: 200px;\r\n            overflow-y: auto;\r\n            scrollbar-width: none;max-height: unset;\r\n            .items{\r\n                display: -webkit-box;\r\n                display: -ms-flexbox;\r\n                display: flex;\r\n                gap: 20px;\r\n                -webkit-box-orient: vertical;\r\n                -webkit-box-direction: normal;\r\n                -ms-flex-direction: column;\r\n                flex-direction: column;\r\n                padding: 0;\r\n                margin: 0;\r\n                .single_items{\r\n                    list-style: none;\r\n                    margin: 0;\r\n                    padding: 0;\r\n                    .hader_popup_link{\r\n                        color: #272222!important;\r\n                        display: -webkit-box;\r\n                        display: -ms-flexbox;\r\n                        display: flex;\r\n                        -webkit-box-align: center;\r\n                        -ms-flex-align: center;\r\n                        align-items: center;\r\n                        gap: 15px;\r\n                        color: #25232c;\r\n                        font-size: 16px;\r\n                        line-height: 21px;\r\n                        font-weight: 600;\r\n                        cursor: pointer;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .popup-footer-btn{\r\n            display: -webkit-box;\r\n            display: -ms-flexbox;\r\n            display: flex;\r\n            gap: 15px;\r\n            -webkit-box-align: center;\r\n            -ms-flex-align: center;\r\n            align-items: center;\r\n            -webkit-box-pack: center;\r\n            -ms-flex-pack: center;\r\n            justify-content: center;\r\n            padding: 15px 25px;\r\n            margin: 0 20px 20px;\r\n            border-radius: 10px;\r\n            background: #f3f2f7;\r\n            a{\r\n                display: -webkit-box;\r\n                display: -ms-flexbox;\r\n                display: flex;\r\n                -webkit-box-align: center;\r\n                -ms-flex-align: center;\r\n                align-items: center;\r\n                gap: 10px;\r\n                color: #464255;\r\n                font-size: 14px;\r\n                line-height: 21px;\r\n                font-weight: 600;\r\n                cursor: pointer;\r\n\r\n                width: 100%;\r\n                -webkit-box-pack: center;\r\n                -ms-flex-pack: center;\r\n                justify-content: center;\r\n                color: #464255!important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nheader{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 13px 29px;\r\n    background: #fff;\r\n    .headerleft{\r\n        .collups-show-icon{\r\n            cursor: pointer;\r\n            i{\r\n                font-size: 20px;\r\n                color: #2D3B29;\r\n                font-weight: 500;\r\n                display: none;\r\n            }\r\n            &.collapsed{\r\n                img{\r\n                    display: none;\r\n                }\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// collups css tyle\r\n.sidebar_left.collapsed{\r\n    left: -273px;\r\n}\r\n\r\n.ekomart_dashboard .right-area-body-content.collapsed {\r\n    width: 100%;\r\n    margin-left: 0;\r\n}\r\n\r\n.submenu .mm-active{\r\n    a{\r\n        background: var(--color-primary);\r\n        color: #fff !important;\r\n        &::after{\r\n            background: #fff !important; \r\n        }\r\n    }\r\n}\r\n\r\n.submenu .mm-active{\r\n    a{\r\n        background: var(--color-primary);\r\n        color: #fff !important;\r\n        &::after{\r\n            background: #fff !important; \r\n        }\r\n    }\r\n}\r\n\r\n\r\n.parent-nav{\r\n    li{\r\n        a.active{\r\n            background: var(--color-primary);\r\n            color: #fff !important;\r\n            &::after{\r\n                background: #fff !important; \r\n            }\r\n        }\r\n    }\r\n}", "\r\n// profile setting area start\r\n.profile-setting-area-main-wrapper{\r\n    padding: 33px;\r\n    @media #{$large-mobile} {\r\n        padding: 8px;\r\n    }\r\n    .title{\r\n        font-size: 20px;\r\n    }\r\n    .inner-profile-setting{\r\n        padding: 30px;\r\n        border: 1px solid #E2E2E2;\r\n        background: #fff;\r\n        border-radius: 8px;\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 23px;\r\n        @media #{$sm-layout} {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n        }\r\n        @media #{$large-mobile} {\r\n            padding: 15px;\r\n        }\r\n        .left-setting-area{\r\n            padding: 40px 12px 12px 12px;\r\n            background: #F3F4F6;\r\n            width: 35%;\r\n            text-align: center;\r\n            border-radius: 10px;\r\n            @media #{$sm-layout} {\r\n                width: 100%;\r\n            }\r\n            .personal-info{\r\n                .thumbnail-img{\r\n                    margin-bottom: 26px;\r\n                }\r\n                .infor{\r\n                    .title{\r\n                        margin-bottom: 10px;\r\n                        font-size: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tab-button-area-setting{\r\n    margin-top: 40px;\r\n    ul{\r\n        padding: 0;\r\n        list-style: none;\r\n        flex-direction: column;\r\n        margin: 0;\r\n        li{\r\n            margin: 0;\r\n            display: block;\r\n            button{\r\n                display: block;\r\n                height: 60px;\r\n                background: #629D23 !important;\r\n                color: #FFFFFF;\r\n                text-align: left;\r\n                border: none !important;\r\n                padding: 0 24px;\r\n                font-weight: 500;\r\n                color: #fff;\r\n                display: block;\r\n                min-width: max-content;\r\n                &.active{\r\n                    color: #fff !important;\r\n                    background: #6DA432 !important;\r\n                }\r\n                &:hover{\r\n                    color: #fff !important;\r\n                    background: #6DA432 !important;\r\n                }\r\n                img{\r\n                    margin-right: 18px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tab-content-area-user-setting{\r\n    border: 1px solid #E2E2E2;\r\n    width: calc(65% - 30px);\r\n    border-radius: 10px;\r\n    padding: 27px;\r\n    @media #{$sm-layout} {\r\n        width: 100%;\r\n    }\r\n    @media #{$large-mobile} {\r\n        padding: 10px;\r\n    }\r\n}\r\n\r\n.inner-content-setting-form{\r\n    .title{\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        margin: 0;\r\n    }\r\n    p{\r\n        margin: 0;\r\n        font-size: 14px;\r\n    }\r\n    form{\r\n        margin-top: 40px;\r\n        .half-input-wrapper{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 25px;\r\n            margin-bottom: 45px;\r\n            @media #{$large-mobile} {\r\n                flex-direction: column;\r\n                align-items: flex-start;\r\n                gap: 20px;\r\n            }\r\n            .single{\r\n                flex-basis: 50%;\r\n                @media #{$large-mobile} {\r\n                    flex-basis: 100%;\r\n                    width: 100%;\r\n                }\r\n                label{\r\n                    color: #2D3B29;\r\n                    font-weight: 500;\r\n                    margin-bottom: 10px;\r\n                }\r\n                input{\r\n                    border: 1px solid #E8E9EB;\r\n                    height: 48px;\r\n                    border-radius: 4px;\r\n                    &:focus{\r\n                        border: 1px solid var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .about-me-area-setting-area{\r\n            label{\r\n                color: #2D3B29;\r\n                font-weight: 500;\r\n                margin-bottom: 10px;\r\n            }\r\n            textarea{\r\n                height: 150px;\r\n                border: 1px solid #E8E9EB;\r\n                text-align: left;\r\n                padding: 15px;\r\n                margin-bottom: 20px;\r\n                border-radius: 4px;\r\n                &:focus{\r\n                    border: 1px solid var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nform.change-pass-form,\r\n.social-media-edit-wrapper{\r\n    .single{\r\n        margin-bottom: 25px;\r\n        &:last-child{\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    label{\r\n        color: #2D3B29;\r\n        font-weight: 500;\r\n        margin-bottom: 10px;\r\n    }\r\n    input{\r\n        border: 1px solid #E8E9EB;\r\n        height: 48px;\r\n        border-radius: 4px;\r\n    }\r\n}\r\n\r\n.about-me-area-setting-area{\r\n    margin-top: 30px;\r\n    .title{\r\n        font-size: 15px;\r\n        margin-bottom: 15px;\r\n    }\r\n    .inner-border-wrapper{\r\n        display: flex;\r\n        padding: 19px;\r\n        border: 1px solid #E8E9EB;\r\n        margin-bottom: 20px;\r\n        padding-bottom: 30px;\r\n        border-radius: 5px;\r\n    }\r\n    .button-area{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 15px;\r\n    }\r\n}\r\n\r\n.profile-image{\r\n    img{\r\n        border-radius: 5px;\r\n        border: 5px solid var(--color-border);\r\n        height: auto;\r\n        width: max-content;\r\n        object-fit: cover;\r\n        max-width: 250px;\r\n        height: 300px;\r\n    }\r\n}\r\n\r\n.brows-file-wrapper {\r\n    position: relative;\r\n    cursor: pointer;\r\n    input{\r\n        position: absolute;\r\n        height: 100%;\r\n        width: 100%;\r\n        opacity: 0;\r\n        cursor: pointer;\r\n        background: var(--background-color-4);\r\n        height: 50px;\r\n        border-radius: 5px;\r\n        color: var(--color-white);\r\n        font-size: 14px;\r\n        padding: 10px 20px;\r\n        border: 2px solid var(--color-border);\r\n        transition: 0.3s;\r\n    }\r\n    label{\r\n        width: max-content;\r\n        padding: 0 16px;\r\n        height: 45px;\r\n        border-radius: 10px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        background: var(--color-primary);\r\n        color: var(--color-white);\r\n        font-weight: 500;\r\n        font-size: 16px;\r\n        transition: .3s;\r\n        position: relative;\r\n        z-index: 10;\r\n    }\r\n}\r\n\r\n.notification__items{\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    gap: 20px;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: normal;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n    padding-left: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n    .single__items{\r\n        list-style: none;\r\n        margin: 0;\r\n        .single-link{\r\n            display: -webkit-box;\r\n            display: -ms-flexbox;\r\n            display: flex;\r\n            -webkit-box-align: center;\r\n            -ms-flex-align: center;\r\n            align-items: center;\r\n            gap: 10px;\r\n            color: var(--body-color);\r\n            font-size: 14px;\r\n            line-height: 21px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            display: -webkit-box;\r\n            display: -ms-flexbox;\r\n            display: flex;\r\n            gap: 15px !important;\r\n            font-size: 16px !important;\r\n            font-weight: 500 !important;\r\n            color: var(--dark-color) !important;\r\n            -webkit-box-align: start !important;\r\n            -ms-flex-align: start !important;\r\n            align-items: flex-start !important;\r\n            .avatar{\r\n                width: 40px;\r\n                height: 40px;\r\n                margin-top: 3px;\r\n                border-radius: 11%;\r\n                background-color: #eceaf3;\r\n                border: none;\r\n            }\r\n            .main-content{\r\n                display: -webkit-box;\r\n                display: -ms-flexbox;\r\n                display: flex;\r\n                -webkit-box-flex: 1;\r\n                -ms-flex: 1;\r\n                flex: 1;\r\n                gap: 5px;\r\n                -webkit-box-orient: vertical;\r\n                -webkit-box-direction: normal;\r\n                -ms-flex-direction: column;\r\n                flex-direction: column;\r\n                -webkit-box-pack: justify;\r\n                -ms-flex-pack: justify;\r\n                justify-content: space-between;\r\n                .name-user{\r\n                    display: -webkit-box;\r\n                    display: -ms-flexbox;\r\n                    display: flex;\r\n                    -webkit-box-pack: justify;\r\n                    -ms-flex-pack: justify;\r\n                    justify-content: space-between;\r\n                    font-size: 15px;\r\n                    line-height: 21px;\r\n                    font-weight: 500;\r\n                    margin-bottom: 0;\r\n                    -webkit-transition: color 0.3s ease;\r\n                    transition: color 0.3s ease;\r\n\r\n                    .time-ago{\r\n                        color: var(--color-primary) !important;\r\n                        font-size: 12px;\r\n                    }\r\n                }\r\n                .disc{\r\n                    display: -webkit-box;\r\n                    display: -ms-flexbox;\r\n                    display: flex;\r\n                    -webkit-box-pack: justify;\r\n                    -ms-flex-pack: justify;\r\n                    justify-content: space-between;\r\n                    font-size: 15px;\r\n                    font-weight: 400;\r\n                    font-size: 14px;\r\n                    .count{\r\n                        width: 10px;\r\n                        height: 10px;\r\n                        background-color: var(--color-primary) !important;\r\n                        display: -webkit-box;\r\n                        display: -ms-flexbox;\r\n                        display: flex;\r\n                        -webkit-box-align: center;\r\n                        -ms-flex-align: center;\r\n                        align-items: center;\r\n                        -webkit-box-pack: center;\r\n                        -ms-flex-pack: center;\r\n                        justify-content: center;\r\n                        width: 10px;\r\n                        height: 10px;\r\n                        border-radius: 100%;\r\n                        font-size: 10px;\r\n                        color: #fff;\r\n                        background: var(--color-danger);\r\n                        margin-top: 5px;\r\n                        animation: zeroone 1s ease-in-out infinite;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@keyframes zeroone {\r\n    from {\r\n        opacity: 0;\r\n    }\r\n    // 50%{\r\n    //     scale: .5;\r\n    // }\r\n    to {\r\n        opacity: 1;\r\n        scale: .5;\r\n    }\r\n  }\r\n\r\n\r\n.apex-chart-top-area-banner{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n}", "\r\n\r\n.input-area-search-head-vendor{\r\n    position: relative;\r\n    input{\r\n        height: 50px;\r\n        border-radius: 5px;\r\n        background: #fff;\r\n        border: 1px solid transparent;\r\n        &:focus{\r\n            border: 1px solid var(--color-primary);\r\n        }\r\n    }\r\n    .rts-btn.btn-primary{\r\n        position: absolute;\r\n        right: 0;\r\n        top: 5px;\r\n        right: 5px;\r\n        height: 40px;\r\n    }\r\n}\r\n\r\n.body-root-inner{\r\n    padding: 40px 30px;\r\n    @media #{$large-mobile} {\r\n        padding: 10px;\r\n    }\r\n}\r\n\r\n.single-vendor-area{\r\n    padding: 40px;\r\n    border: 1px solid #E2E2E2;\r\n    border-radius: 6px;\r\n    height: 100%;\r\n    background: #fff;\r\n    @media #{$laptop-device} {\r\n        padding: 20px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 20px;\r\n    }\r\n    @media #{$large-mobile} {\r\n        padding: 25px;\r\n    }\r\n    .logo-vendor{\r\n        max-width: max-content;\r\n        height: auto;\r\n        margin-bottom: 20px;\r\n        img{\r\n            max-width: max-content;\r\n            height: auto;\r\n            @media #{$large-mobile} {\r\n                max-width: 110px;\r\n            }\r\n        }\r\n    }\r\n    .title{\r\n        font-size: 24px;\r\n        margin-bottom: 15px;\r\n        @media #{$laptop-device} {\r\n            font-size: 20px;\r\n        }\r\n        span{\r\n            padding: 4px 12px;\r\n            background: var(--color-primary);\r\n            font-size: 14px;\r\n            color: #fff;\r\n            border-radius: 2px;\r\n            margin-left: 10px;\r\n            font-weight: 400;\r\n            &.closed{\r\n                background: #DC2626;\r\n            }\r\n        }\r\n    }\r\n    .stars-area{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 2px;\r\n        margin-bottom: 30px;\r\n        i{\r\n            color: #FF9A00;\r\n        }\r\n        span{\r\n            margin-left: 8px;\r\n            color: #74787C;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n    .location{\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 10px;\r\n        margin-bottom: 15px;\r\n        &:last-child{\r\n            margin-bottom: 0;\r\n        }\r\n        i{\r\n            font-size: 20px;\r\n        }\r\n        p{\r\n            max-width: 70%;\r\n            @media #{$large-mobile} {\r\n                max-width: 100%;\r\n            }\r\n        }\r\n    }\r\n    a.rts-btn{\r\n        margin-top: 30px;\r\n    }\r\n}\r\n\r\n\r\n.vendor-list-main-wrapper{\r\n    padding: 30px;\r\n    border-radius: 10px;\r\n    background: #fff;\r\n    border: 1px solid #E2E2E2;\r\n    margin-top: 20px;\r\n    @media #{$smlg-device} {\r\n        overflow: scroll;\r\n        .table-responsive{\r\n            width: 1000px;\r\n        }\r\n    }\r\n    @media #{$small-mobile} {\r\n        padding: 6px;\r\n    }\r\n}\r\n\r\n.vendor-list-main-wrapper{\r\n    thead{\r\n        border: none;\r\n        tr{\r\n            border: none;\r\n            th{\r\n                border: none;\r\n                margin-bottom: 20px;\r\n                padding-bottom: 38px;\r\n            }\r\n        }\r\n    }\r\n    tbody{\r\n        tr{\r\n            td{\r\n                padding: 20px 0;\r\n                img{\r\n                    max-width: 54px;\r\n                    cursor: pointer;\r\n                }\r\n                .itemside{\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 25px;\r\n                    text-decoration: none;\r\n                    .info{\r\n                        .title{\r\n                            margin-bottom: 7px;\r\n                            text-decoration: none;\r\n                        }\r\n                        .stars-wrapper{\r\n                            display: flex;\r\n                            .stars{\r\n                                display: flex;\r\n                                align-items: center;\r\n                                color: #FF9A00;\r\n                                i{\r\n                                    color: #FF9A00;\r\n                                }\r\n                            }\r\n                            span{\r\n                                margin-left: 10px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                p{\r\n                    margin: 0;\r\n                }\r\n                .rts-btn{\r\n                    text-decoration: none;\r\n                }\r\n                .open{\r\n                    padding: 5px 10px;\r\n                    display: block;\r\n                    background: #E0EBD3;\r\n                    text-align: center;\r\n                    color: var(--color-primary);\r\n                    font-weight: 600;\r\n                    border-radius: 2px;\r\n                }\r\n                .close{\r\n                    padding: 5px 10px;\r\n                    display: block;\r\n                    background: #FAD8CF;\r\n                    text-align: center;\r\n                    color: #F05C54;\r\n                    font-weight: 600;\r\n                    border-radius: 2px;\r\n                }\r\n            }\r\n            &:hover{\r\n                --bs-table-accent-bg: transparent;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.vendor-list-p .vendor-list-main-wrapper tbody tr td .rts-btn {\r\n    text-decoration: none;\r\n    margin-left: auto;\r\n}\r\n\r\n.vendor-banner-left{\r\n    background: #2C3C28;\r\n    border-radius: 6px;\r\n    height: 100%;\r\n    text-align: center;\r\n    padding: 40px 25px;\r\n    .stars-area{\r\n        display: flex;\r\n        align-items: center;\r\n        color: #fff;\r\n        i{\r\n            color: #FF9A00;\r\n        }\r\n        span{\r\n            margin-left: 10px;\r\n            color: #fff;\r\n        }\r\n    }\r\n    .location{\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 15px;\r\n        margin-top: 25px;\r\n        i{\r\n            color: #fff;\r\n        }\r\n        p{\r\n            color: #fff;\r\n            text-align: left;\r\n        }\r\n    }\r\n}\r\n\r\n.banner-vendor-details{\r\n    background-image: url(../images/vendor/01.webp);\r\n    height: 400px;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    padding: 40px;\r\n    .content-area{\r\n        .title{\r\n            margin-top: 10px;\r\n            margin-bottom: 10px;\r\n            font-size: 36px;\r\n            @media #{$large-mobile} {\r\n                font-size: 26px;\r\n            }\r\n            span{\r\n                color: var(--color-primary);\r\n                font-weight: 400;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.shop-now-goshop-btn{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    .plus-icon{\r\n        height: 30px;\r\n        width: 30px;\r\n        background: var(--color-primary);\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        i{\r\n            color: #fff;\r\n        }\r\n    }\r\n    span{\r\n        font-weight: 700;\r\n        color: #232722;\r\n    }\r\n}\r\n\r\n.shop-now-goshop-btn{\r\n    max-width: max-content;\r\n    .plus-icon{\r\n        opacity: 0;\r\n        display: flex;\r\n        transition: opacity 0.4s 0.25s, transform 0.6s 0.25s;\r\n        transition-timing-function: cubic-bezier(0.1, 0.75, 0.25, 1);\r\n    }\r\n    .plus-icon + .plus-icon {\r\n        margin-inline-end: 0;\r\n        margin-inline-start: 8px;\r\n        display: flex;\r\n        margin-inline-start: 0;\r\n        margin-inline-end: 0;\r\n        opacity: 1;\r\n        transform: translateX(-0px);\r\n        transition-delay: 0s;\r\n        order: -2;\r\n    }\r\n    .text{\r\n        display: flex;\r\n        transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);\r\n        margin-inline-start: -1px;\r\n    }\r\n    &:hover{\r\n        .text{\r\n            transition-delay: 0.0s;\r\n            transform: translateX(-33px);\r\n        }\r\n        .plus-icon + .plus-icon{\r\n            opacity: 0;\r\n            transform: translateX(-30px);\r\n            transition-delay: 0.0s;\r\n        }\r\n        .plus-icon{\r\n            opacity: 1;\r\n            transition-delay: 0s;\r\n            transform: translateX(-30px);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.product-area-add-wrapper {\r\n    padding: 30px 0;\r\n    position: relative;\r\n    border-radius: 6px;\r\n    margin-top: 30px;\r\n    background-image: url(../images/vendor/02.webp);\r\n    .title{\r\n        margin-left: -50px;\r\n        color: #fff;\r\n        text-align: center;\r\n        font-size: 48px;\r\n    }\r\n    .one{\r\n        position: absolute;\r\n        right: 50px;\r\n        bottom: 0;\r\n        height: 100%;\r\n        @media #{$smlg-device} {\r\n            max-width: 29%;\r\n            height: auto;\r\n        }\r\n        @media #{$md-layout} {\r\n            display: none;\r\n        }\r\n        @media #{$sm-layout} {\r\n            display: none;\r\n        }\r\n    }\r\n    .two{\r\n        position: absolute;\r\n        left: 10%;\r\n        bottom: 0;\r\n        @media #{$md-layout} {\r\n            display: none;\r\n        }\r\n        @media #{$sm-layout} {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.order-details-table-1-table{\r\n    padding: 30px;\r\n    padding-bottom: 30px;\r\n    border: 1px solid #E2E2E2;\r\n    margin-top: 30px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    @media #{$md-layout} {\r\n        overflow: auto;\r\n    }\r\n    @media #{$sm-layout} {\r\n        overflow: auto;\r\n    }\r\n    table{\r\n        @media #{$md-layout} {\r\n            overflow-x: auto;\r\n            -webkit-overflow-scrolling: touch;\r\n            width: 1100px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            overflow-x: auto;\r\n            -webkit-overflow-scrolling: touch;\r\n            width: 1100px;\r\n        }\r\n    }\r\n    .order-details-table{\r\n        thead{\r\n            border: none;\r\n            tr{\r\n                border: none;\r\n                border-bottom: 1px solid #E8E9EB;\r\n                th{\r\n                    border: none;\r\n                    margin-bottom: 15px;\r\n                    padding-bottom: 15px;\r\n                }\r\n            }\r\n        }\r\n        tr{\r\n            border-bottom: 1px solid #E8E9EB;\r\n            &.b-n{\r\n                border: none;\r\n            }\r\n            td{\r\n                padding: 10px;\r\n                .item{\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 15px;\r\n                    .discription{\r\n                        .title{\r\n                            margin-bottom: 2px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.card-body.table-product-select{\r\n    @media #{$smlg-device} {\r\n        overflow: auto;\r\n        table{\r\n            width: 900px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.top-product-wrapper-scroll{\r\n    @media screen and (max-width: 1250px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 900px;\r\n        }\r\n    }\r\n    @media screen and (max-width: 750px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 800px;\r\n        }\r\n    }\r\n}\r\n.rop-product-right{\r\n    @media screen and (max-width: 1250px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 500px;\r\n        }\r\n    }\r\n    @media screen and (max-width: 1200px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 100%;\r\n        }\r\n    }\r\n}\r\n.best-shop-seller-top-scroll{\r\n    @media screen and (max-width: 1250px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 800px;\r\n        }\r\n    }\r\n    @media screen and (max-width: 1200px) {\r\n        overflow: auto;\r\n        width: 100%;\r\n        .top-product-area-start{\r\n            width: 100%;\r\n        }\r\n    }\r\n    @media screen and (max-width: 991px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 100%;\r\n        }\r\n    }\r\n    @media screen and (max-width: 691px) {\r\n        overflow: auto;\r\n        .top-product-area-start{\r\n            width: 550px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n.vendor-list-main-wrapper tbody tr td{\r\n    &:nth-child(2){\r\n        width: 30%;\r\n    }\r\n}\r\n\r\n\r\n#example_wrapper{\r\n    .dataTables_length{\r\n        padding: 20px;\r\n        label{\r\n            display:flex;\r\n            align-items: center;\r\n            gap: 10px;\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n    #example_filter{\r\n        padding: 20px;\r\n        label{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 10px;\r\n            font-weight: 500;\r\n            font-size: 18px;\r\n        }\r\n        input{\r\n            height: 35px;\r\n            border: 1px solid #f1f1f1;\r\n        }\r\n    }\r\n    #example_info{\r\n        padding: 20px;\r\n    }\r\n    #example_paginate{\r\n        padding: 20px;\r\n    }\r\n    .paginate_button.current{\r\n        background: var(--color-primary) !important;\r\n        color: #fff !important;\r\n        border: 1px solid var(--color-primary) !important;\r\n    }\r\n    .paginate_button:hover{\r\n        background: var(--color-primary) !important;\r\n        color: #fff !important;\r\n        border: 1px solid var(--color-primary) !important;\r\n    }\r\n    .paginate_button{\r\n        background: #fff !important;\r\n        color: var(--color-primary);\r\n        border: 1px solid #fff !important;\r\n        // box-shadow: 4px 4px 6px #0000001a;\r\n        border: 1px solid #f1f1f1 !important;\r\n    }\r\n}\r\ntable.dataTable.no-footer {\r\n    border-bottom: 1px solid #f1f1f1;\r\n}\r\n\r\n\r\n.transiction-filter{\r\n    thead{\r\n        tr{\r\n            th{\r\n                padding: 0 !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n.table-transixtion{\r\n    .dataTables_length{\r\n        padding: 20px 0 !important;\r\n    }\r\n    #example_wrapper #example_info{\r\n        padding: 20px 0 !important;\r\n    }\r\n    #example_wrapper #example_paginate{\r\n        padding: 20px 0 !important;\r\n    }\r\n}\r\n\r\ntable.dataTable thead .sorting {\r\n    position: relative;\r\n    background-image: none;\r\n    max-width: max-content;\r\n    &::after{\r\n        content: \"\";\r\n        right: auto;\r\n        position: absolute;\r\n        background-image: url(../images/form/01.png);\r\n        height: 19px;\r\n        width: 19px;\r\n        background-repeat: no-repeat;\r\n        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);\r\n    }\r\n}\r\n\r\ntable.dataTable thead .sorting_asc {\r\n    position: relative;\r\n    background-image: none;\r\n    max-width: max-content;\r\n    &::after{\r\n        content: \"\";\r\n        right: auto;\r\n        position: absolute;\r\n        background-image: url(../images/form/01.png);\r\n        height: 19px;\r\n        width: 19px;\r\n        background-repeat: no-repeat;\r\n        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);\r\n    }\r\n}\r\n\r\ntable.dataTable thead .sorting_asc {\r\n   background-image: none !important;\r\n}\r\ntable.dataTable thead .sorting_desc {\r\n    background-image: none !important;\r\n    position: relative;\r\n    background-image: none;\r\n    max-width: max-content;\r\n    &::after{\r\n        content: \"\";\r\n        right: 0;\r\n        position: absolute;\r\n        background-image: url(../images/form/02.png);\r\n        height: 19px;\r\n        width: 19px;\r\n        background-repeat: no-repeat;\r\n        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);\r\n    }\r\n}\r\n\r\n\r\n\r\n.order-page .vendor-list-main-wrapper tbody tr td:nth-child(2) {\r\n    width: 17%;\r\n}\r\n\r\n\r\n.between-stock-table.statrusts{\r\n    position: relative;\r\n    .action-edit-deleate{\r\n        position: absolute;\r\n        top: 52%;\r\n        right: 24px;\r\n        border: 1px solid #f1f1f1;\r\n        z-index: 10;\r\n        display: none;\r\n        border-radius: 5px;\r\n        span{\r\n            &:last-child{\r\n                border: none;\r\n            }\r\n            display: block;\r\n            max-width: 100%;\r\n            padding: 17px 25px;\r\n            background: #fff;\r\n            border-bottom: 1px solid #f1f1f1;\r\n            font-weight: 500;\r\n            color: #74787C;\r\n            cursor: pointer;\r\n            transition: .3s;\r\n            &:hover{\r\n                background: var(--color-primary);\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n}\r\n.between-stock-table.action{\r\n    position: relative;\r\n    .action-edit-deleate{\r\n        position: absolute;\r\n        top: 52%;\r\n        right: 24px;\r\n        border: 1px solid #f1f1f1;\r\n        z-index: 10;\r\n        display: none;\r\n        border-radius: 5px;\r\n        span{\r\n            &:last-child{\r\n                border: none;\r\n            }\r\n            display: block;\r\n            max-width: 100%;\r\n            padding: 17px;\r\n            background: #fff;\r\n            border-bottom: 1px solid #f1f1f1;\r\n            font-weight: 500;\r\n            color: #74787C;\r\n            cursor: pointer;\r\n            transition: .3s;\r\n            &:hover{\r\n                background: var(--color-primary);\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.p-d-page .vendor-list-main-wrapper tbody tr td:nth-child(2) {\r\n    width: 15%;\r\n}", "\r\n// style here\r\n\r\n\r\n\r\n.rts-btn{\r\n    max-width: max-content;\r\n    padding: 14px 25px;\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n    color: #fff;\r\n    &.btn-primary{\r\n        background: #629D23;\r\n        border-radius: 6px;\r\n        display: block;\r\n        max-width: max-content;\r\n    }\r\n    &.radious-sm{\r\n        border-radius: 6px;\r\n    }\r\n    &.with-icon{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n        .arrow-icon{\r\n            display: inline-block;\r\n            transition: opacity 0.4s 0.25s, transform 0.6s 0.25s;\r\n            transition-timing-function: cubic-bezier(0.1, 0.75, 0.25, 1);\r\n        }\r\n        .arrow-icon + .arrow-icon {\r\n            margin-inline-end: 0;\r\n            margin-inline-start: 8px;\r\n            display: inline-block;\r\n            margin-inline-start: 0;\r\n            margin-inline-end: 0;\r\n            opacity: 0;\r\n            transform: translateX(-10px);\r\n            transition-delay: 0s;\r\n            order: -2;\r\n        }\r\n        .btn-text{\r\n            display: inline-block;\r\n            transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);\r\n            margin-inline-start: -23px;\r\n        }\r\n        &:hover{\r\n            .btn-text{\r\n                transition-delay: 0.1s;\r\n                transform: translateX(23px);\r\n            }\r\n            .arrow-icon + .arrow-icon{\r\n                opacity: 1;\r\n                transform: translateX(0);\r\n                transition-delay: 0.225s;\r\n            }\r\n            .arrow-icon{\r\n                opacity: 0;\r\n                transition-delay: 0s;\r\n                transform: translateX(10px);\r\n            }\r\n        }\r\n    }\r\n}\r\n", ".nice-select {\r\n    -webkit-tap-highlight-color: transparent;\r\n    background-color: #fff;\r\n    border-radius: 5px;\r\n    border: solid 1px #e8e8e8;\r\n    box-sizing: border-box;\r\n    clear: both;\r\n    cursor: pointer;\r\n    display: block;\r\n    float: left;\r\n    font-family: inherit;\r\n    font-size: 14px;\r\n    font-weight: normal;\r\n    height: 42px;\r\n    line-height: 40px;\r\n    outline: none;\r\n    padding-left: 18px;\r\n    padding-right: 30px;\r\n    position: relative;\r\n    text-align: left !important;\r\n    -webkit-transition: all 0.2s ease-in-out;\r\n    transition: all 0.2s ease-in-out;\r\n    -webkit-user-select: none;\r\n       -moz-user-select: none;\r\n        -ms-user-select: none;\r\n            user-select: none;\r\n    white-space: nowrap;\r\n    width: auto; }\r\n    .nice-select:hover {\r\n      border-color: #dbdbdb; }\r\n    .nice-select:active, .nice-select.open, .nice-select:focus {\r\n      border-color: #999; }\r\n    .nice-select:after {\r\n      border-bottom: 2px solid #999;\r\n      border-right: 2px solid #999;\r\n      content: '';\r\n      display: block;\r\n      height: 5px;\r\n      margin-top: -4px;\r\n      pointer-events: none;\r\n      position: absolute;\r\n      right: 12px;\r\n      top: 50%;\r\n      -webkit-transform-origin: 66% 66%;\r\n          -ms-transform-origin: 66% 66%;\r\n              transform-origin: 66% 66%;\r\n      -webkit-transform: rotate(45deg);\r\n          -ms-transform: rotate(45deg);\r\n              transform: rotate(45deg);\r\n      -webkit-transition: all 0.15s ease-in-out;\r\n      transition: all 0.15s ease-in-out;\r\n      width: 5px; }\r\n    .nice-select.open:after {\r\n      -webkit-transform: rotate(-135deg);\r\n          -ms-transform: rotate(-135deg);\r\n              transform: rotate(-135deg); }\r\n    .nice-select.open .list {\r\n      opacity: 1;\r\n      pointer-events: auto;\r\n      -webkit-transform: scale(1) translateY(0);\r\n          -ms-transform: scale(1) translateY(0);\r\n              transform: scale(1) translateY(0); }\r\n    .nice-select.disabled {\r\n      border-color: #ededed;\r\n      color: #999;\r\n      pointer-events: none; }\r\n      .nice-select.disabled:after {\r\n        border-color: #cccccc; }\r\n    .nice-select.wide {\r\n      width: 100%; }\r\n      .nice-select.wide .list {\r\n        left: 0 !important;\r\n        right: 0 !important; }\r\n    .nice-select.right {\r\n      float: right; }\r\n      .nice-select.right .list {\r\n        left: auto;\r\n        right: 0; }\r\n    .nice-select.small {\r\n      font-size: 12px;\r\n      height: 36px;\r\n      line-height: 34px; }\r\n      .nice-select.small:after {\r\n        height: 4px;\r\n        width: 4px; }\r\n      .nice-select.small .option {\r\n        line-height: 34px;\r\n        min-height: 34px; }\r\n    .nice-select .list {\r\n      background-color: #fff;\r\n      border-radius: 5px;\r\n      box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);\r\n      box-sizing: border-box;\r\n      margin-top: 4px;\r\n      opacity: 0;\r\n      overflow: hidden;\r\n      padding: 0;\r\n      pointer-events: none;\r\n      position: absolute;\r\n      top: 100%;\r\n      right: 0;\r\n      -webkit-transform-origin: 50% 0;\r\n          -ms-transform-origin: 50% 0;\r\n              transform-origin: 50% 0;\r\n      -webkit-transform: scale(0.75) translateY(-21px);\r\n          -ms-transform: scale(0.75) translateY(-21px);\r\n              transform: scale(0.75) translateY(-21px);\r\n      -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;\r\n      transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;\r\n      z-index: 69; }\r\n      .nice-select .list:hover .option:not(:hover) {\r\n        background-color: transparent !important; }\r\n    .nice-select .option {\r\n      cursor: pointer;\r\n      font-weight: 400;\r\n      line-height: 40px;\r\n      list-style: none;\r\n      min-height: 40px;\r\n      outline: none;\r\n      padding-left: 18px;\r\n      padding-right: 29px;\r\n      text-align: left;\r\n      -webkit-transition: all 0.2s;\r\n      transition: all 0.2s; }\r\n      .nice-select .option:hover, .nice-select .option.focus, .nice-select .option.selected.focus {\r\n        background-color: #f6f6f6; }\r\n      .nice-select .option.selected {\r\n        font-weight: bold; }\r\n      .nice-select .option.disabled {\r\n        background-color: transparent;\r\n        color: #999;\r\n        cursor: default; }\r\n  \r\n  .no-csspointerevents .nice-select .list {\r\n    display: none; }\r\n  \r\n  .no-csspointerevents .nice-select.open .list {\r\n    display: block; }\r\n  \r\n\r\n\r\n\r\n\r\n.input-between-search-transiction{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    input{\r\n        height: 50px;\r\n        border-radius: 4px;\r\n        border: 1px solid #E2E2E2;\r\n        width: 362px;\r\n        padding:15px;\r\n        display: flex;\r\n        align-items: center;\r\n    }\r\n}\r\n\r\n\r\n.table-transixtion{\r\n    th {\r\n        border: none;\r\n        margin-bottom: 20px;\r\n        padding-bottom: 20px !important;\r\n    }\r\n    tbody{\r\n        .payment{\r\n            gap: 10px;\r\n        }\r\n        .id{\r\n            color: #74787C;\r\n            font-weight: 500;\r\n        }\r\n        td{\r\n            p{\r\n                font-weight: 500;\r\n            }\r\n            .btn-primary{\r\n                padding: 8px 12px;\r\n                border-radius: 4px;\r\n                transition: .3s;\r\n                border: 1px solid transparent;\r\n                &:hover{\r\n                    background: transparent;\r\n                    border: 1px solid var(--color-primary);\r\n                    color: #629D23;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.button-wrapper-reviews{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n    .rts-btn{\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n.ratings-area-reviews-tablwe{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 5px;\r\n    i{\r\n        color: #FF9A00;\r\n    }\r\n}\r\n\r\n.title-right-actioin-btn-wrapper-product-list{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .button-wrapper{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 15px;\r\n        button{\r\n            height: 37px;\r\n            display: flex;\r\n            align-items: center;\r\n        }\r\n        .nice-select{\r\n            height: 37px;\r\n            display: flex;\r\n            align-items: center;\r\n        }\r\n    }\r\n}\r\n\r\n.product-top-filter-area-l{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 35px;\r\n    @media #{$large-mobile} {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 15px;\r\n    }\r\n    .left-area-button-fiulter{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 9px;\r\n        .signle-product-single-button{\r\n            span{\r\n                display: block;\r\n                padding: 5px 10px;\r\n                border: 1px solid #C6C9CC;\r\n                color: #595F69;\r\n                font-weight: 500;\r\n                border-radius: 5px;\r\n                cursor: pointer;\r\n                transition: .3s;\r\n                &:hover{\r\n                    background: var(--color-primary);\r\n                    border: 1px solid var(--color-primary);\r\n                    color: #fff;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.right-filter-upload-area{\r\n    svg{\r\n        cursor: pointer;\r\n    }\r\n}\r\n\r\n", ".single-shopping-card-one.tranding-product{\r\n    display: flex;\r\n    align-items: flex-start;\r\n    gap: 18px;\r\n    background: #FFFFFF;\r\n    margin-bottom: 15px;\r\n    @media #{$laptop-device} {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n    }\r\n    @media #{$smlg-device} {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n    }\r\n    @media #{$large-mobile} {\r\n        flex-direction: column;\r\n    }\r\n    &:last-child{\r\n        margin-bottom: 0;\r\n    }\r\n    .thumbnail-preview{\r\n        border: 1px solid #EAEAEA;\r\n        height: 130px;\r\n        min-width: 130px;\r\n        @media #{$laptop-device} {\r\n            height: auto;\r\n            width: 100%;\r\n        }\r\n        @media #{$smlg-device} {\r\n            height: auto;\r\n                width: 100%;\r\n        }\r\n        @media #{$large-mobile} {\r\n            width: 100%;\r\n            height: auto;\r\n        }\r\n        img{\r\n            height: 130px;\r\n            width: 130px;   \r\n             @media #{$laptop-device} {\r\n                height: auto;\r\n                width: 100%;\r\n            }\r\n            @media #{$smlg-device} {\r\n                height: auto;\r\n                width: 100%;\r\n            }\r\n            @media #{$large-mobile} {\r\n                width: 100%;\r\n                height: auto;\r\n            }\r\n        }\r\n        .badge{\r\n            left: 7px;\r\n            i{\r\n                font-size: 48px;\r\n            }\r\n        }\r\n    }\r\n    .body-content{\r\n        padding-top: 0;\r\n    }\r\n    .time-tag{\r\n        margin-bottom: 9px;\r\n    }\r\n    .price-area{\r\n        margin-top: 8px;\r\n    }\r\n    .body-content a .title{\r\n        margin-bottom: 5px;\r\n    }\r\n    .cart-counter-action{\r\n        margin-top: 10px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.single-shopping-card-one{\r\n    padding: 15px;\r\n    background: #F5F6F7;\r\n    border-radius: 6px;\r\n    .image-and-action-area-wrapper{\r\n        position: relative;\r\n        .action-share-option{\r\n            position: absolute;\r\n            bottom: -0px;\r\n            left: 50%;\r\n            transform: translateX(-50%) rotateX(-90deg);\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n            height: 48px;\r\n            border-radius: 10px 10px 0 0;\r\n            background: var(--color-primary);\r\n            padding: 10px 29px;\r\n            transform-origin: bottom;\r\n            transition: .4s cubic-bezier(0.375, 1.185, 0.92, 0.975);\r\n            .single-action{\r\n                height: 28px;\r\n                width: 28px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border: 1.2px dashed rgba(255, 255, 255, 0.42);\r\n                border-radius: 50%;\r\n                transition: all .3s;\r\n                i{\r\n                    color: #fff;\r\n                    transition: all .3s;\r\n                }\r\n                &:hover{\r\n                    background: #fff;\r\n                    i{\r\n                        color: var(--color-primary);\r\n                        animation: 0.5s mymove;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .thumbnail-preview{\r\n        border-radius: 6px;\r\n        overflow: hidden;\r\n        display: block;\r\n        position: relative;\r\n        .badge{\r\n            position: absolute;\r\n            left: 30px;\r\n            top: -10px;\r\n            z-index: 5;\r\n            i{\r\n                color: #EABC5E;\r\n                font-size: 50px;\r\n            }\r\n            span{\r\n                position: absolute;\r\n                top: 17px;\r\n                left: 17px;\r\n                font-size: 11px;\r\n                line-height: 1.1;\r\n                color: #2C3C28;\r\n                text-align: left;\r\n                font-weight: 700;\r\n            }\r\n        }\r\n        img{\r\n            width: 100%;\r\n            transition: .3s;\r\n            transform: scale(1.01);\r\n        }\r\n        .action-share-option{\r\n            position: absolute;\r\n            bottom: -0px;\r\n            left: 50%;\r\n            transform: translateX(-50%) rotateX(-90deg);\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n            height: 48px;\r\n            border-radius: 10px 10px 0 0;\r\n            background: var(--color-primary);\r\n            padding: 10px 29px;\r\n            transform-origin: bottom;\r\n            transition: .4s cubic-bezier(0.375, 1.185, 0.92, 0.975);\r\n            .single-action{\r\n                height: 28px;\r\n                width: 28px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border: 1.2px dashed rgba(255, 255, 255, 0.42);\r\n                border-radius: 50%;\r\n                transition: all .3s;\r\n                i{\r\n                    color: #fff;\r\n                    transition: all .3s;\r\n                }\r\n                &:hover{\r\n                    background: #fff;\r\n                    i{\r\n                        color: var(--color-primary);\r\n                        animation: 0.5s mymove;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .body-content{\r\n        padding-top: 15px;\r\n        .time-tag{\r\n            padding: 3px 8px;\r\n            background: #FFFFFF;\r\n            border: 1px solid rgba(43, 66, 38, 0.12);\r\n            box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.04);\r\n            border-radius: 4px;\r\n            max-width: max-content;\r\n            i{\r\n                color: #2C3C28;\r\n                font-weight: 500;\r\n            }\r\n            font-size: 10px;\r\n            color: #2C3C28;\r\n            font-weight: 600;\r\n            margin-bottom: 15px;\r\n        }\r\n        a{\r\n            .title{\r\n                transition: .3s;\r\n                font-size: 16px;\r\n                margin-bottom: 10px;\r\n            }\r\n            &:hover{\r\n                .title{\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n        .availability{\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n        }\r\n        .price-area{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n            margin-top: 8px;\r\n            .current{\r\n                font-weight: 700;\r\n                color: var(--color-danger);\r\n                font-size: 20px;\r\n                margin-bottom: 0;\r\n            }\r\n            .previous{\r\n                margin-bottom: 0;\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #74787C;\r\n                position: relative;\r\n                &::after{\r\n                    position: absolute;\r\n                    overflow: auto;\r\n                    left: -5%;\r\n                    top: 50%;\r\n                    content: '';\r\n                    height: 1px;\r\n                    width: 110%;\r\n                    background: #74787C;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .cart-counter-action{\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        gap: 7px;\r\n        margin-top: 10px;\r\n        flex-wrap: wrap;\r\n        .quantity-edit{\r\n            width: 92px;\r\n            display: flex;\r\n            align-items: center;\r\n            border: 1px solid rgba(43, 66, 38, 0.12);\r\n            border-radius: 4px;\r\n            padding: 2px 10px;\r\n            justify-content: space-between;\r\n            background: #fff;\r\n            box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.04);\r\n            .button-wrapper-action{\r\n                border: 1px solid rgba(43, 66, 38, 0.12);\r\n                border-radius: 2px;\r\n                background: #fff;\r\n                display: flex;\r\n            }\r\n            input{\r\n                padding: 0;\r\n                max-width: 10px;\r\n                font-weight: 600;\r\n            }\r\n            button{\r\n                padding: 0;\r\n                max-width: max-content;\r\n                font-size: 0;\r\n                i{\r\n                    font-size: 10px;\r\n                    padding: 4px 6px;\r\n                    transition: .3s;\r\n                }\r\n                &:first-child{\r\n                    i{\r\n                        border-right: 1px solid rgba(43, 66, 38, 0.12);\r\n                    }\r\n                }\r\n                &:hover{\r\n                    i{\r\n                        background: var(--color-primary);\r\n                        color: #fff;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .rts-btn{\r\n            font-size: 14px;\r\n            padding: 8px 16px !important;\r\n            background: transparent;\r\n            color: #629D23;\r\n            border: 1px solid #629D23;\r\n            @media #{$extra-device} {\r\n                padding: 9px 10px !important;\r\n                font-size: 11px;\r\n            }\r\n            @media #{$laptop-device} {\r\n                padding: 10px 7px !important;\r\n                font-size: 10px;\r\n            }\r\n            i{\r\n                transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);\r\n            }\r\n            &:hover{\r\n                background: var(--color-primary);\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n    &:hover{\r\n        .thumbnail-preview{\r\n            img{\r\n                transform: scale(1.1);\r\n            }\r\n    \r\n        }\r\n        .action-share-option{\r\n            bottom: 0;\r\n            transform: translateX(-50%) rotateX(0deg);\r\n        }\r\n    }\r\n    &.deals-of-day{\r\n        background: #fff;\r\n        border: 1px solid #E2E2E2;\r\n        overflow: hidden;\r\n        position: relative;\r\n        .onsale-offer{\r\n            span{\r\n                display: block;\r\n                position: absolute;\r\n                padding: 5px 15px;\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                color: #fff;\r\n                background: var(--color-primary);\r\n                border-radius: 6px 0 6px 0;\r\n                z-index: 10;\r\n                left: 0;\r\n                top: 0;\r\n                letter-spacing: 1px;\r\n                text-transform: uppercase;\r\n            }\r\n        }\r\n        &:hover{\r\n            .thumbnail-preview{\r\n                img{\r\n                    transform: scale(1.2);\r\n                }\r\n            }\r\n        }\r\n        .start-area-rating{\r\n            margin-bottom: 10px;\r\n            i{\r\n                color: #FF9A00;\r\n            }\r\n        }\r\n        .thumbnail-preview{\r\n            border: none !important;\r\n            img{\r\n                width: 100%;\r\n                transform: scale(1.01);\r\n            }\r\n        }\r\n        .cart-counter-action .rts-btn {\r\n            background: #629d23;\r\n            color: #629D23;\r\n            border: 1px solid #629D23;\r\n            width: 100%;\r\n            max-width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            .btn-text{\r\n                color: #fff;\r\n            }\r\n            .arrow-icon{\r\n                i{\r\n                    color: #fff;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.product-filter-area-vendors-details{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    @media #{$sm-layout} {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: 10px;\r\n    }\r\n    form{\r\n        position: relative;\r\n        input{\r\n            height: 50px;\r\n            border: 1px solid #D9D9D9;\r\n            border-radius: 5px;\r\n            width: 470px;\r\n            @media #{$sm-layout} {\r\n                width: 320px;\r\n            }\r\n            @media #{$large-mobile} {\r\n                width: 290px;\r\n            }\r\n        }\r\n        a{\r\n            position: absolute;\r\n            right: 5px;\r\n            height: 40px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.vendor-list-main-wrapper.product-wrapper{\r\n    padding: 0;\r\n    .card-body{\r\n        padding: 0;\r\n        thead{\r\n            border-radius: 5px 5px 0 0;\r\n            overflow: hidden;\r\n            tr{\r\n                background: var(--color-primary);\r\n                border-radius: 5px 5px 0 0;\r\n                th{\r\n                    padding: 14px 20px;\r\n                    color: #fff;\r\n                }\r\n            }\r\n        }\r\n        tbody{\r\n            tr{\r\n                td{\r\n                    padding: 20px;\r\n                    line-height: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.item-check-area-table-left{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 28px;\r\n    .form-check{\r\n        input{\r\n            height: 28px !important;\r\n            width: 28px;\r\n        }\r\n    }\r\n    .item-image-and-name{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 19px;\r\n        .thumbnail{\r\n            min-width: max-content;\r\n            img{\r\n                max-width: max-content;\r\n                min-width: max-content;\r\n            }\r\n        }\r\n        p{\r\n            color: #2D3B29;\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    p{\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\n.between-stock-table{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n}", ".apexcharts-tooltip.apexcharts-theme-light {\r\n    border: 1px solid #e3e3e3;\r\n    background: rgba(255, 255, 255, 0.96);\r\n}\r\n.apexcharts-tooltip {\r\n    border-radius: 5px;\r\n    box-shadow: 2px 2px 6px -4px #999;\r\n    cursor: default;\r\n    font-size: 14px;\r\n    left: 62px;\r\n    opacity: 0;\r\n    pointer-events: none;\r\n    position: absolute;\r\n    top: 20px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    white-space: nowrap;\r\n    z-index: 12;\r\n    transition: 0.15s ease all;\r\n}\r\n.apexcharts-tooltip {\r\n    border-color: transparent;\r\n    border: none !important;\r\n    overflow: visible !important;\r\n    border-radius: 10px !important;\r\n}\r\n\r\n.apexcharts-tooltip .custom-tooltip {\r\n    position: relative;\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: normal;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n    gap: 5px;\r\n    padding: 15px 18px;\r\n    border-radius: 10px;\r\n    background-color: #fff;\r\n    border: none !important;\r\n    overflow: visible;\r\n    -webkit-box-shadow: 0 18px 16px rgba(0, 0, 0, 0.06);\r\n    box-shadow: 0 18px 16px rgba(0, 0, 0, 0.06);\r\n    // &:before{\r\n    //     content: '';\r\n    //     position: absolute;\r\n    //     bottom: -12px;\r\n    //     left: 50%;\r\n    //     -webkit-transform: translateX(-50%);\r\n    //     transform: translateX(-50%);\r\n    //     border-left: 10px solid transparent;\r\n    //     border-right: 10px solid transparent;\r\n    //     border-top: 12px solid #fff;\r\n    // }\r\n}\r\n\r\n.apexcharts-tooltip .custom-tooltip__title {\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    font-weight: 400;\r\n    margin-bottom: 0;\r\n    color: var(--body-color);\r\n}\r\n\r\n.apexcharts-tooltip .custom-tooltip__subtitle {\r\n    font-size: 10px;\r\n    line-height: 15px;\r\n    font-weight: 400;\r\n    margin-bottom: 0;\r\n    color: var(--success-color);\r\n}\r\n\r\n\r\n.apex-xhart-area-one{\r\n    padding: 25px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    @media #{$large-mobile} {\r\n        padding: 10px;\r\n    }\r\n}\r\n\r\n.top-product-area-start{\r\n    padding: 30px;\r\n    border-radius: 10px;\r\n    background: #fff;\r\n    @media #{$laptop-device} {\r\n        padding: 20px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding: 20px;\r\n    }\r\n    .between-area-top{\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        @media #{$laptop-device} {\r\n            flex-wrap: wrap;\r\n            gap: 15px;\r\n        }\r\n        @media #{$smlg-device} {\r\n            flex-direction: column;\r\n            gap: 15px;\r\n            align-items: flex-start;\r\n        }\r\n        .title{\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n.product-top-area-single{\r\n    display: flex;\r\n    align-items: center;\r\n    margin: 20px 0;\r\n    gap: 10px;\r\n    .image-area{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 14px;\r\n        flex-basis: 40%;\r\n        .thumbnail{\r\n            max-width: 60px;\r\n            img{\r\n                max-width: 60px;\r\n                border: 1px solid #F2F3F5;\r\n                border-radius: 5px;\r\n            }\r\n        }\r\n        .information{\r\n            p{\r\n                margin-bottom: 10px;\r\n                font-weight: 600;\r\n                color: #2C3C28;\r\n                @media #{$laptop-device} {\r\n                    font-size: 14px;\r\n                }\r\n                @media #{$smlg-device} {\r\n                    font-size: 14px;\r\n                }\r\n            }\r\n            span{\r\n                min-width: max-content;\r\n                @media #{$laptop-device} {\r\n                    font-size: 13px;\r\n                }\r\n                @media #{$smlg-device} {\r\n                    font-size: 14px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &>div{\r\n        p{\r\n            margin-bottom: 10px;\r\n            font-weight: 600;\r\n            color: #2C3C28;\r\n            min-width: max-content;\r\n            @media #{$laptop-device} {\r\n                font-size: 14px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n    .coupon-code,\r\n    .logo,\r\n    .indec{\r\n        flex-basis: 20%;\r\n        display: flex;\r\n        @media #{$laptop-device} {\r\n            margin-left: 7px;\r\n        }\r\n        img{\r\n\r\n        }\r\n    }\r\n    &.bottom{\r\n        .image-area{\r\n            flex-basis: 50%;\r\n        }\r\n        .coupon-code,\r\n        .logo,\r\n        .indec{\r\n            flex-basis: 25%;\r\n            display: flex;\r\n            img{\r\n\r\n            }\r\n        }\r\n    }\r\n    .logo{\r\n        display: flex;\r\n        justify-content: center;\r\n    }\r\n    .indec{\r\n        display: flex;\r\n        align-items: flex-start;\r\n        gap: 15px;\r\n        justify-content: flex-end;\r\n        margin-right: 40px;\r\n    }\r\n}\r\n\r\n.sale-statictics-button{\r\n    ul{\r\n        list-style: none;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        border: none;\r\n    }\r\n    li{\r\n        button{\r\n            border-radius: 6px !important;\r\n            padding: 5px 15px;\r\n            border: 1px solid #C6C9CC !important;\r\n            color: #595F69;\r\n            font-weight: 500;\r\n            &.active{\r\n                background: var(--color-primary) !important;\r\n                color: #fff !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.apex-chart-top-area-banner{\r\n    .title-top{\r\n        font-size: 20px;\r\n    }\r\n}\r\n\r\n.add-product-page{\r\n    padding: 30px !important;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "\r\n// cart style hear\r\n.cart-bar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    background: #fff;\r\n    width: 360px;\r\n    height: 100%;\r\n    padding: 20px;\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: normal;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n    -webkit-box-shadow: 0 0 20px rgb(0 0 0 / 20%);\r\n    box-shadow: 0 0 20px rgb(0 0 0 / 20%);\r\n    visibility: hidden;\r\n    -webkit-transform: translateX(100%);\r\n    transform: translateX(100%);\r\n    -webkit-transition: all 500ms ease;\r\n    transition: all 500ms ease;\r\n    z-index: 1000;\r\n}\r\n.cart-bar.show {\r\n    visibility: visible;\r\n    -webkit-transform: translateX(0);\r\n    transform: translateX(0);\r\n}\r\n.cart-bar .cart-header {\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-align: center;\r\n    -ms-flex-align: center;\r\n    align-items: center;\r\n    -webkit-box-pack: justify;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 15px;\r\n    border-bottom: 1px solid #E7E7E7;\r\n}\r\n.cart-bar .cart-header .cart-heading {\r\n    font-size: 17px;\r\n    font-weight: 600;\r\n    color: #000000;\r\n    font-family: var(--font-secondary);\r\n}\r\n.close-cart {\r\n    cursor: pointer;\r\n    margin-top: -11px;\r\n    i{\r\n        transition: .3s;\r\n    }\r\n    &:hover{\r\n        i{\r\n            transform: scale(1.3);\r\n            color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n.cart-bar .product-item {\r\n    width: 100%;\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-align: center;\r\n    -ms-flex-align: center;\r\n    align-items: center;\r\n    -webkit-box-pack: justify;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    padding-bottom: 12px;\r\n    margin-bottom: 12px;\r\n    border-bottom: 1px solid #E7E7E7;\r\n}\r\n.product-item {\r\n    margin-bottom: 65px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n.cart-bar .product-item:last-child {\r\n    margin-bottom: 0;\r\n}\r\n.cart-bar .product-detail {\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-align: center;\r\n    -ms-flex-align: center;\r\n    align-items: center;\r\n    -webkit-box-pack: justify;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n}\r\n.cart-bar .cart-edit {\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: normal;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n    -webkit-box-align: end;\r\n    -ms-flex-align: end;\r\n    align-items: flex-end;\r\n}\r\n\r\n.cart-edit {\r\n    margin-right: 20px;\r\n}\r\n.cart-bar .product-detail .product-thumb {\r\n    margin-right: 15px;\r\n    max-width: 75px;\r\n}\r\n.cart-bar .cart-edit .quantity-edit {\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    border: 1px solid #E7E7E7;\r\n    padding: 3px 10px;\r\n    border-radius: 5px;\r\n    margin-bottom: 3px;\r\n}\r\n.cart-edit .quantity-edit {\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    padding: 3px 10px;\r\n    border-radius: 5px;\r\n}\r\n.cart-bar .cart-edit .item-wrapper {\r\n    display: inline-block;\r\n}\r\n.cart-bar .cart-edit .quantity-edit button {\r\n    background: none;\r\n    font-size: 0;\r\n}\r\n.cart-bar .cart-edit .quantity-edit button i {\r\n    font-size: 14px;\r\n    color: #C0C0C0;\r\n}\r\n.cart-edit .quantity-edit button i {\r\n    font-size: 16px;\r\n    color: #d3d3d3;\r\n}\r\nedit .quantity-edit button {\r\n    background: none;\r\n    font-size: 0;\r\n}\r\n.cart-edit .quantity-edit button {\r\n    background: none;\r\n    font-size: 0;\r\n}\r\n.cart-bar .cart-edit .quantity-edit button i {\r\n    font-size: 14px;\r\n    color: #000000;\r\n    &:hover{\r\n        color: var(--color-primary  );\r\n    }\r\n}\r\n.cart-edit .quantity-edit button i {\r\n    font-size: 16px;\r\n    color: #d3d3d3;\r\n}\r\n\r\n.cart-bar .cart-edit .quantity-edit input {\r\n    text-align: center;\r\n    max-width: 26px;\r\n    padding: 0;\r\n}\r\n.cart-edit .quantity-edit input {\r\n    text-align: center;\r\n    max-width: 55px;\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n    color: #040404;\r\n}\r\n.cart-bar .cart-edit .product-edit {\r\n    margin-right: 15px;\r\n}\r\n.cart-bar .product-detail .product-name {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: var(--color-primary);\r\n}\r\n.cart-bar .product-detail span {\r\n    display: inline-block;\r\n    line-height: 19px !important;\r\n}\r\n.cart-bar .product-detail .product-variation span {\r\n    color: #868686;\r\n    font-family: roboto;\r\n    font-weight: 400;\r\n    font-size: 13px;\r\n    line-height: 15px;\r\n}\r\n\r\n.cart-bar .product-detail .product-qnty, .cart-bar .product-detail .product-price {\r\n    color: #404040;\r\n    font-weight: 500;\r\n    font-size: 13px;\r\n    font-family: roboto;\r\n}\r\n\r\n.cart-bar .cart-bottom-area {\r\n    margin-top: auto;\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: normal;\r\n    -ms-flex-direction: column;\r\n    flex-direction: column;\r\n}\r\n\r\n.cart-bar .cart-bottom-area .spend-shipping {\r\n    margin-bottom: 30px;\r\n    background: #F5F5F5;\r\n    padding: 10px 15px;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    color: var(--color-primary);\r\n    text-align: center;\r\n}\r\n.cart-bar .cart-bottom-area .spend-shipping i {\r\n    font-size: 15px;\r\n    margin-right: 7px;\r\n}\r\n.cart-bar .cart-bottom-area .spend-shipping .amount {\r\n    font-weight: 700;\r\n    color: #040404;\r\n}\r\n.cart-bar .cart-bottom-area .total-price {\r\n    font-size: 18px;\r\n    color: #040404;\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: flex;\r\n    -webkit-box-pack: justify;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    margin-bottom: 20px;\r\n    span{\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n.cart-bar .cart-bottom-area .checkout-btn {\r\n    border: 1px solid var(--color-primary);\r\n    margin-bottom: 10px;\r\n    color: var(--color-primary);\r\n}\r\n.cart-bar .cart-bottom-area .cart-btn {\r\n    width: 100%;\r\n    padding: 10px 20px;\r\n    border-radius: 7px;\r\n    font-size: 14px;\r\n    text-align: center;\r\n    -webkit-transition: all 300ms;\r\n    transition: all 300ms;\r\n}\r\n.cart-bar .cart-bottom-area .view-btn {\r\n    border: 1px solid transparent;\r\n    background: var(--color-primary);\r\n    color: #fff;\r\n}\r\n.cart-bar .cart-bottom-area .cart-btn {\r\n    width: 100%;\r\n    padding: 10px 20px;\r\n    border-radius: 7px;\r\n    font-size: 14px;\r\n    text-align: center;\r\n    -webkit-transition: all 300ms;\r\n    transition: all 300ms;\r\n}\r\n\r\n.cart-bar .cart-bottom-area .checkout-btn:hover {\r\n    background: var(--color-primary);\r\n    color: #fff;\r\n}\r\n\r\n\r\n// side  abar style hear\r\n\r\n.side-bar {\r\n    position: fixed;\r\n    overflow: hidden;\r\n    top: 0;\r\n    right: -100%;\r\n    width: 465px;\r\n    padding: 40px 30px;\r\n    padding-top: 50px;\r\n    height: 100%;\r\n    display: block;\r\n    background-color: white;\r\n    backdrop-filter: blur(7px);\r\n    z-index: 1900;\r\n    transition: all 600ms ease;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow-y: auto;\r\n    overflow: visible;\r\n    @media #{$large-mobile} {\r\n        width: 320px;\r\n    }\r\n    .inner-main-wrapper-desk{\r\n        .thumbnail{\r\n            display: flex;\r\n            justify-content: center;\r\n            img{\r\n                width: 85%;\r\n                margin: auto;\r\n            }\r\n        }\r\n        .inner-content{\r\n            text-align: center;\r\n            margin-top: 30px;\r\n            p{\r\n            max-width: 95%;\r\n            text-align: center;\r\n            margin: auto;\r\n            }\r\n            .title{\r\n                font-weight: 600;\r\n            }\r\n            .footer{\r\n                padding-top: 50px;\r\n                margin-top: 80px;\r\n                border-top: 1px solid #c2c2c2;\r\n                .title{\r\n                    font-weight: 500;\r\n                }\r\n                a.rts-btn{\r\n                    margin: auto;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.side-bar.show {\r\n    right: 0;\r\n    overflow-y: auto;\r\n}\r\n.side-bar button.close-icon-menu {\r\n    max-width: max-content;\r\n    margin-right: auto;\r\n    margin-left: auto;\r\n    margin-top: -50px;\r\n    position: absolute;\r\n    height: 45px;\r\n    width: 45px;\r\n    background: var(--color-primary);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    right: 0;\r\n    i{\r\n        color: #fff;\r\n        height: 50px;\r\n        width: 50px;\r\n        border-radius: 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-left: 0;\r\n        margin-top: 0;\r\n        font-size: 22px;\r\n    }\r\n}\r\n\r\n\r\n#anywhere-home.bgshow {\r\n    background: #0e1013;\r\n    opacity: 70%;\r\n    visibility: visible;\r\n    pointer-events: visible;\r\n    z-index: 999;\r\n    top: 0;\r\n}\r\n#anywhere-home {\r\n    cursor: url(../images/banner/shape/close.png), auto;\r\n    background: #0e1013;\r\n    position: fixed;\r\n    width: 100%;\r\n    height: 100%;\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    transition: opacity 500ms ease-in-out;\r\n    pointer-events: none;\r\n    z-index: 50;\r\n}\r\n\r\n\r\n\r\n\r\n.right-collups-add-product{\r\n    .right-collups-area-top{\r\n        .title{\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            margin-bottom: 8px;\r\n        }\r\n        p{\r\n            font-weight: 500;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n    .input-main-wrapper{\r\n        .single-input{\r\n            margin-top: 38px;\r\n            label{\r\n                margin-bottom: 11px;\r\n                font-size: 16px;\r\n                color: #74787C;\r\n            }\r\n            input,\r\n            textarea{\r\n                height: 62px;\r\n                border: 1px solid #E8E9EB;\r\n                border-radius: 4px;\r\n            }\r\n            textarea{\r\n                height: 106px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.file-upload-add-product{\r\n    .profile-left{\r\n        position: relative;\r\n        width: 100%;\r\n        height: 100%;\r\n        .profile-image{\r\n            position: relative;\r\n            span{\r\n                position: absolute;\r\n                left: 50%;\r\n                top: 68%;\r\n                pointer-events: none;\r\n                z-index: -1;\r\n                transform: translate(-50%, -50%);\r\n            }\r\n        }\r\n        .profile-image img{\r\n            height: auto;\r\n            height: 150px;\r\n            object-fit: scale-down;\r\n            border: 1px dashed #74787C;\r\n            min-width: 100%;\r\n        }\r\n        .button-area{\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            cursor: pointer;\r\n            z-index: 100;\r\n            pointer-events: none;\r\n        }\r\n    }\r\n}\r\n\r\n.button-area-botton-wrapper-p-list{\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n    button{\r\n        margin: 0;\r\n        position: relative;\r\n        border: 1px solid var(--color-primary);\r\n        &.bg-transparent{\r\n            border: 1px solid var(--color-primary);\r\n            color: var(--color-primary);\r\n            &:hover{\r\n                background: var(--color-primary) !important;\r\n                color: #fff !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.between-stock-table{\r\n    &.statrus{\r\n        p{\r\n            margin: 0;\r\n            padding: 7px 15px;\r\n            border-radius: 33px;\r\n            background: #EFF5E9;\r\n            color: #629D23;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.footer-copyright{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 25px;\r\n    background: #fff;\r\n    margin-bottom: -41px;\r\n    margin-top: 20px;\r\n    border-radius: 7px 7px 0 0;\r\n    border: 1px solid #E2E2E2;\r\n    @media #{$sm-layout} {\r\n        flex-direction: column;\r\n        align-items: center;\r\n        gap: 15px;\r\n    }\r\n    .left{\r\n        p{\r\n            margin: 0;\r\n            padding: 0;\r\n            color: #2D3B29;\r\n            font-weight: 500;\r\n        }\r\n    }\r\n    ul{\r\n        list-style: none;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 45px;\r\n        margin: 0;\r\n        padding: 0;\r\n        li{\r\n            margin: 0;\r\n            a{\r\n                color: #629D23;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.flex-direction-column{\r\n    flex-direction: column;\r\n}", "// search style\r\n.search-input-area {\r\n    transition: all 500ms ease;\r\n    visibility: hidden;\r\n    transform: translateY(-100%);\r\n    opacity: 0;\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    padding: 57px 0;\r\n    background: white;\r\n    box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.46);\r\n    z-index: 9999;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n.search-input-area.show {\r\n    visibility: visible;\r\n    transform: inherit;\r\n    opacity: 1;\r\n}\r\n.search-input-area .search-input-inner {\r\n    display: flex;\r\n    align-items: center;\r\n    position: relative;\r\n}\r\n.search-input-area .search-input-inner .input-div {\r\n    width: 80%;\r\n    display: flex;\r\n    align-items: center;\r\n    margin: auto;\r\n}\r\n.search-input-area .search-input-inner .input-div input {\r\n    background: #F7F7F7;\r\n    border-radius: 5px;\r\n    height: 55px;\r\n    border: 1px solid transparent;\r\n    &:focus{\r\n        border: 1px solid var(--color-primary);\r\n    }\r\n}\r\n.search-input-area .search-input-inner .input-div button {\r\n    max-width: max-content;\r\n    padding: 18px 21px;\r\n    background: var(--color-primary);\r\n    display: flex;\r\n    color: #fff;\r\n    align-items: center;\r\n    justify-content: center;\r\n    display: block;\r\n    margin-left: -9px;\r\n    border-radius: 0 5px 5px 0;\r\n}\r\n.search-input-area .search-close-icon {\r\n    cursor: pointer;\r\n    position: absolute;\r\n    right: 38px;\r\n    top: 22px;\r\n}\r\n.search-input-area .search-close-icon i {\r\n    position: relative;\r\n    z-index: 1;\r\n    color: var(--color-primary);\r\n    transition: 0.3s;\r\n    font-size: 18px;\r\n    &:hover{\r\n        color: #F7F7F7;\r\n        &::after{\r\n            background: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n.search-input-area .search-close-icon i::after {\r\n    position: absolute;\r\n    height: 45px;\r\n    width: 45px;\r\n    content: \"\";\r\n    border-radius: 5px;\r\n    background: #553cdf14;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: -1;\r\n    transition: 0.3s;\r\n}\r\n\r\n#anywhere-home {\r\n    cursor: url(../images/banner/shape/close.png), auto;\r\n    background: #0e1013;\r\n    position: fixed;\r\n    width: 100%;\r\n    height: 100%;\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    transition: opacity 500ms ease-in-out;\r\n    pointer-events: none;\r\n    z-index: 50;\r\n}\r\n\r\n#anywhere-home.bgshow {\r\n    background: #0e1013;\r\n    opacity: 70%;\r\n    visibility: visible;\r\n    pointer-events: visible;\r\n    z-index: 999;\r\n    top: 0;\r\n}", "// back to top style scss\r\n.progress-wrap {\r\n\tposition: fixed;\r\n\tright: 30px;\r\n\tbottom: 30px;\r\n\theight: 46px;\r\n\twidth: 46px;\r\n\tcursor: pointer;\r\n\tdisplay: block;\r\n\tborder-radius: 50px;\r\n\tz-index: 10000;\r\n\topacity: 1;\r\n\tvisibility: hidden;\r\n\ttransform: translateY(15px);\r\n\t-webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n\r\n}\r\n.progress-wrap.active-progress {\r\n\topacity: 1;\r\n\tvisibility: visible;\r\n\ttransform: translateY(0);\r\n\tbox-shadow: #629d2310 0px 0px 6px 7px;\r\n}\r\n.progress-wrap::after {\r\n\tposition: absolute;\r\n\tfont-family: var(--font-three);\r\n\tcontent: '\\f077';\r\n\ttext-align: center;\r\n\tline-height: 46px;\r\n\tfont-size: 16px;\r\n\tcolor: var(--color-primary);\r\n\tleft: 0;\r\n\ttop: 0;\r\n\theight: 46px;\r\n\twidth: 46px;\r\n\tcursor: pointer;\r\n\tdisplay: block;\r\n\tz-index: 1;\r\n\t-webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n    border: 0px solid var(--color-primary);\r\n    box-shadow: none;\r\n\tborder-radius: 50% !important;\r\n\tborder-radius: 5px;\r\n    font-weight: 300;\r\n}\r\n.progress-wrap:hover::after {\r\n\topacity: 1;\r\n    content: '\\f077';\r\n    border: 0px solid var(--color-primary);\r\n    font-weight: 300;\r\n}\r\n.progress-wrap::before {\r\n\tposition: absolute;\r\n\tfont-family: var(--font-three);\r\n\tcontent: '\\f077';\r\n\ttext-align: center;\r\n\tline-height: 46px;\r\n\tfont-size: 16px;\r\n\topacity: 0;\r\n\tbackground: var(--color-primary);\r\n\t-webkit-background-clip: text;\r\n\t-webkit-text-fill-color: transparent;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\theight: 46px;\r\n\twidth: 46px;\r\n\tcursor: pointer;\r\n\tdisplay: block;\r\n\tz-index: 2;\r\n\t-webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n    font-weight: 300;\r\n}\r\n.progress-wrap:hover::before {\r\n\topacity: 0;\r\n}\r\n.progress-wrap svg path { \r\n\tfill: none; \r\n}\r\n.progress-wrap svg { \r\n    color: var(--color-primary);\r\n\tborder-radius: 50%;\r\n\tbackground: #fff;\r\n}\r\n.progress-wrap svg.progress-circle path {\r\n    stroke: var(--color-primary);\r\n    stroke-width: 0px;\r\n\tbox-sizing:border-box;\r\n\t-webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n    padding: 2px;\r\n}\r\n\r\n\r\n\r\n.home-blue{\r\n\t.progress-wrap svg.progress-circle path{\r\n\t\tstroke: var(--color-primary-2);\r\n\t}\r\n\t.progress-wrap::after{\r\n\t\tborder-color: var(--color-primary-2);\r\n\t\tbox-shadow: 0px 3px 20px 6px #0742e952;\r\n\t\tcolor: var(--color-primary-2);\r\n\t}\r\n}\r\n\r\n", "\r\n// registration style\r\n\r\n.registration-wrapper-1{\r\n    max-width: 800px;\r\n    border-radius: 20px;\r\n    background: #FFFFFF;\r\n    margin: auto;\r\n    text-align: center;\r\n    padding: 100px 150px;\r\n    @media #{$sm-layout} {\r\n        padding: 80px 50px;\r\n    }\r\n    @media #{$large-mobile} {\r\n        padding: 40px 15px;\r\n    }\r\n    .logo-area{\r\n        img{\r\n            max-width: 57px;\r\n            margin: auto;\r\n            margin-bottom: 30px;\r\n        }\r\n    }\r\n}\r\n\r\n.registration-form{\r\n    text-align: left;\r\n    .input-wrapper{\r\n        width: 100%;\r\n        text-align: left;\r\n        margin-bottom: 30px;\r\n        &:last-child{\r\n            margin-bottom: 0;\r\n        }\r\n        label{\r\n            margin-bottom: 12px;\r\n            font-weight: 500;\r\n            color: #2C3C28;\r\n        }\r\n        input{\r\n            border-radius: 5px;\r\n            border: 1px solid  #EBEBEB;\r\n            height: 50px;\r\n            &:focus{\r\n                border: 1px solid var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n    button.rts-btn{\r\n        max-width: 100%;\r\n        width: 100%;\r\n    }\r\n    .another-way-to-registration{\r\n        .registradion-top-text{\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-top: 30px;\r\n            margin-bottom: 30px;\r\n            span{\r\n                font-weight: 500;\r\n                color: #2C3C28;\r\n            }\r\n            position: relative;\r\n            &::after{\r\n                position: absolute;\r\n                content: '';\r\n                left: 0;\r\n                width: 35%;\r\n                height: 1px;\r\n                background: #E7E7E7;\r\n                @media #{$sm-layout} {\r\n                    display: none;\r\n                }\r\n            }\r\n            &::before{\r\n                position: absolute;\r\n                content: '';\r\n                right: 0%;\r\n                width: 35%;\r\n                height: 1px;\r\n                background: #E7E7E7;\r\n                @media #{$sm-layout} {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n        .login-with-brand{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 10px;\r\n            &>a{\r\n                flex-basis: 49%;\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n                height: 50px;\r\n                border-radius: 6px;\r\n                border: 1px solid #EBEBEB;\r\n            }\r\n        }\r\n        p{\r\n            margin: 0;\r\n            display: flex;\r\n            justify-content: center;\r\n            margin-top: 30px;\r\n            font-size: 16px;\r\n            a{\r\n                color: #2C3C28;\r\n                font-weight: 600;\r\n                margin-left: 10px;\r\n            }\r\n        }\r\n    }\r\n}", "\r\n// brand area start\r\n\r\n.single-brand-area-start{\r\n    text-align: center;\r\n    border: 1px solid #f1f1f1;\r\n    padding: 40px 25px;\r\n    border-radius: 6px;\r\n    .logo{\r\n        margin-bottom: 26px;\r\n    }\r\n    .item{\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.rating-table{\r\n    ul{\r\n        list-style: none;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        margin: 0;\r\n        gap: 3px;\r\n        color: #eeae00;\r\n        li{\r\n            margin: 0;\r\n            padding: 0;\r\n            i{\r\n                color: #eeae00;\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.rating-page tbody tr td:nth-child(2) {\r\n    width: 15%;\r\n}", "\r\n// order details area start\r\n\r\n\r\n.customers-details-wrapper-one-dashboard{\r\n    padding: 30px;\r\n    .title{\r\n        font-size: 20px;\r\n        margin-bottom: 15px;\r\n    }\r\n    .main-customers-details-top{\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 146px;\r\n        border-bottom: 1px solid #E2E2E2;\r\n        padding-bottom: 35px;\r\n        @media #{$laptop-device} {\r\n            gap: 25px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n        }\r\n        @media #{$smlg-device} {\r\n            gap: 25px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n        }\r\n        .left{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 19px;\r\n            .name{\r\n                margin-bottom: 5px;\r\n            }\r\n        }\r\n        .right-area{\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 120px;\r\n            justify-content: space-between;\r\n            width: 100%;\r\n            @media #{$laptop-device} {\r\n                gap: 65px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                gap: 30px;\r\n            }\r\n            .short-contact-info{\r\n                p{\r\n                    margin-bottom: 10px;\r\n                }\r\n                a{\r\n                    font-weight: 500;\r\n                    color: #2D3B29;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.billing-address-area-4{\r\n    padding:0 30px;\r\n    padding-bottom: 30px;\r\n    border-bottom: 1px solid #E2E2E2;\r\n    margin-bottom: 30px;\r\n    &:last-child{\r\n        border: none;\r\n    }\r\n    .main-billing-address-wrapper{\r\n        display: flex;\r\n        align-items: flex-start;\r\n        flex-wrap: wrap;\r\n        gap: 15px;\r\n        .single-billing-address{\r\n            flex-basis: 25%;\r\n            @media #{$md-layout} {\r\n                flex-basis:45%;\r\n            }\r\n            @media #{$sm-layout} {\r\n                flex-basis:45%;\r\n            }\r\n            @media #{$large-mobile} {\r\n                flex-basis: 100%;\r\n            }\r\n            p{\r\n                margin-bottom: 10px;\r\n                span{\r\n                    font-weight: 500;\r\n                    color: var(--color-heading-1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.single-over-fiew-card{\r\n    padding: 26px;\r\n    border: 1px solid #E2E2E2;\r\n    border-radius: 5px;\r\n    background: #FFFFFF;\r\n    height: 100%;\r\n}\r\n\r\n\r\n.single-over-fiew-card{\r\n    .top-main{\r\n        \r\n        color: #74787C;\r\n        font-weight: 500;\r\n    }\r\n    .bottom{\r\n        display: flex;\r\n        align-items: center;\r\n        margin-top: 15px;\r\n        gap: 40px;\r\n        @media #{$extra-device} {\r\n            gap: 15px;\r\n        }\r\n        @media #{$laptop-device} {\r\n            gap: 15px;\r\n        }\r\n        @media #{$smlg-device} {\r\n            gap: 5px;\r\n        }\r\n        .title{\r\n            font-size: 38px;\r\n            line-height: 1.2;\r\n            margin-bottom: 0;\r\n            word-break: keep-all;\r\n            @media #{$extra-device} {\r\n                font-size: 26px;\r\n            }\r\n            @media #{$laptop-device} {\r\n                font-size: 26px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                font-size: 26px;\r\n            }\r\n        }\r\n        .right-primary{\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            gap: 40px;\r\n            @media #{$laptop-device} {\r\n                gap: 5px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                gap: 5px;\r\n            }\r\n            img{\r\n                @media #{$laptop-device} {\r\n                    max-width: 40px;\r\n                }\r\n            }\r\n            .increase{\r\n                i{\r\n                    color: var(--color-primary);\r\n                }\r\n                span{\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n            img{\r\n                @media #{$smlg-device} {\r\n                    max-width: 40px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}"]}