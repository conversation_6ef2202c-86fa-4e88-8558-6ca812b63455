<?php
require __DIR__ . '/init.php';

$products = rcf_products();
$productSuccess = rcf_flash('product_success');
?>
<!DOCTYPE html>
<html lang="en">
    <!-- rts header area start -->
    <head>
        <meta charset="UTF-8">
        <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="Grocery, Store, stores">
        <title>RC Furnishing Admin</title>
        <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
        <!-- plugins css -->
        <link rel="stylesheet" href="assets/css/plugins.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css">
        <link rel="stylesheet" href="assets/css/style.css">
</head>
    <!-- rts header area end -->
<body class="p-d-page">
<?php include __DIR__ . '/includes/admin-header.php'; ?>

            <div class="body-root-inner">
                <div class="transection">
                    <div class="title-right-actioin-btn-wrapper-product-list">
                        <h3 class="title">Product</h3>
                        <div class="button-wrapper">
                            <div class="single-select">
                                <div class="nice-select" tabindex="0"><span class="current">Export</span>
                                    <ul class="list">
                                        <li data-value="Default Sorting" data-display="Export" class="option selected">Export</li>
                                        <li data-value="1" class="option">CSV</li>
                                        <li data-value="2" class="option">PDF</li>
                                    </ul>
                                </div>
                            </div>
                            <button class="rts-btn btn-primary menu-btn">+ Add</button>
                        </div>
                    </div>
                    <div class="product-top-filter-area-l">
                        <div class="left-area-button-fiulter">
                            <!-- single product button -->
                             <div class="signle-product-single-button">
                                <span>All 250 </span>
                             </div>
                            <!-- single product button end -->
                            <!-- single product button -->
                             <div class="signle-product-single-button">
                                <span>New Item 150</span>
                             </div>
                            <!-- single product button end -->
                            <!-- single product button -->
                             <div class="signle-product-single-button">
                                <span>Disabled 154</span>
                             </div>
                            <!-- single product button end -->
                        </div>
                    </div>
                    <div class="vendor-list-main-wrapper product-wrapper">
                        <div class="card-body table-product-select">
    <?php if (!empty($productSuccess)): ?>
        <div class="auth-feedback success" style="margin-bottom: 24px;">
            <?php echo htmlspecialchars($productSuccess, ENT_QUOTES); ?>
        </div>
    <?php endif; ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="example" class="display" cellspacing="0" width="100%">
                                    <thead class="mb--20">
                                        <tr>
                                            <th>Product Name</th>
                                            <th>Product  No</th>
                                            <th>Category</th>
                                            <th>Price</th>
                                            
                                            <th>Date</th>
                                            <th class="text-start">Stock</th>
                                        </tr>
                                    </thead>
                                    <tbody>
    <?php if (empty($products)): ?>
        <tr>
            <td colspan="6">No products found. Add a new product to get started.</td>
        </tr>
    <?php else: ?>
        <?php foreach ($products as $product): ?>
            <?php
            $rawImage = trim((string)($product['image'] ?? ''));
            if ($rawImage === '') {
                $rawImage = 'assets/images/store/01.jpg';
            }
            $image = rcf_admin_asset_url($rawImage);
            $createdAt = isset($product['created_at']) ? date('m/d/Y', strtotime((string)$product['created_at'])) : '—';
            $price = number_format((float)($product['price'] ?? 0), 2);
            ?>
            <tr>
                <td width="30%">
                    <div class="item-check-area-table-left">
                        <div class="item-image-and-name">
                            <a href="#" class="thumbnail">
                                <img src="<?php echo htmlspecialchars($image, ENT_QUOTES); ?>" alt="product">
                            </a>
                            <p><?php echo htmlspecialchars($product['name'], ENT_QUOTES); ?></p>
                        </div>
                    </div>
                </td>
                <td>
                    <p>#<?php echo htmlspecialchars($product['sku'], ENT_QUOTES); ?></p>
                </td>
                <td>
                    <div class="payment d-flex align-items-center">
                        <p><?php echo htmlspecialchars($product['category'] ?: '—', ENT_QUOTES); ?></p>
                    </div>
                </td>
                <td>
                    <p>$<?php echo $price; ?></p>
                </td>
                <td><p><?php echo htmlspecialchars($createdAt, ENT_QUOTES); ?></p></td>
                <td class="text-end">
                    <div class="between-stock-table action">
                        <p><?php echo (int)($product['stock'] ?? 0); ?></p>
                        <span class="status-badge" style="margin-left:12px; text-transform: capitalize;">
                            <?php echo htmlspecialchars($product['status'] ?? 'draft', ENT_QUOTES); ?>
                        </span>
                    </div>
                </td>
            </tr>
        <?php endforeach; ?>
    <?php endif; ?>
</tbody>
                                </table>
                                <!-- table-responsive.// -->
                            </div>
                        </div>
                     </div>
                </div>
                <!-- bottom footer areas start -->
                <div class="footer-copyright">
                    <div class="left">
                        <p>Copyright © 2024 All Right Reserved.</p>
                    </div>
                    <ul>
                        <li><a href="#">Terms</a></li>
                        <li><a href="#">Privacy</a></li>
                        <li><a href="#">Help</a></li>
                    </ul>
                </div>
                <!-- bottom footer areas end -->
            </div>

        </div>

    </div>



  <!-- rts header area start -->
  


<!-- progress area start -->
<div class="progress-wrap active-progress">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
    </svg>
</div>
<!-- progress area end -->




<div class="search-input-area">
    <div class="container">
        <div class="search-input-inner">
            <div class="input-div">
                <input class="search-input autocomplete ui-autocomplete-input" type="text" placeholder="Search by keyword or #" autocomplete="off">
                <button><i class="far fa-search"></i></button>
            </div>
        </div>
    </div>
    <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
</div>


<div id="anywhere-home"></div>

<div id="side-bar" class="side-bar header-two right-collups-add-product">
    <button class="close-icon-menu"><i class="far fa-times"></i></button>
    <!-- inner menu area desktop start -->
    <div class="right-collups-area-top">
        <h6 class="title">Add New Product</h6>
        <p>Add information and add new product</p>
    </div>
    <div class="input-main-wrapper">
        <div class="single-input">
            <label for="one">Product Name</label>
            <input type="text" id="one" placeholder="Quaker Oats Healthy Meal...">
        </div>
        <div class="single-input">
            <label for="Two">Regular Price</label>
            <input type="text" id="Two" placeholder="240">
        </div>
        <div class="single-input">
            <label for="sale">Sale Price</label>
            <input type="text" id="sale" placeholder="$250">
        </div>
        <div class="single-input">
            <label for="Stock">Stock</label>
            <input type="text" id="Stock" placeholder="530">
        </div>
        <div class="single-input">
            <label for="sku">SKU</label>
            <input type="text" id="sku" placeholder="3245">
        </div>
        <div class="single-input">
            <label for="cate">Category</label>
            <input type="text" id="cate" placeholder="Notebook">
        </div>
        <div class="single-input">
            <label for="Tag">Tag</label>
            <input type="text" id="Tag" placeholder="Iphone, Mobile">
        </div>
        <div class="single-input">
            <label for="text">Description</label>
            <textarea name="text" id="text" placeholder="Type something"></textarea>
        </div>
        <div class="single-input">
            <div class="file-upload-add-product">
                <div class="profile-left">
                    <div class="profile-image mb--30">
                        <img id="rts_image" src="assets/images/grocery/16.png" alt="Profile-NFT">
                        <span>Drag and drop Image</span>
                    </div>
                    <div class="button-area">
                        <div class="brows-file-wrapper">
                            <!-- actual upload which is hidden -->
                            <input name="rts_images1" id="rts_images1" type="file">
                            <!-- our custom upload button -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-area-botton-wrapper-p-list">
            <button class="rts-btn btn-primary">Save</button>
            <button class="rts-btn btn-primary bg-transparent">Cancel</button>
        </div>
    </div>
</div>
<?php include __DIR__ . '/includes/admin-footer.php'; ?>
