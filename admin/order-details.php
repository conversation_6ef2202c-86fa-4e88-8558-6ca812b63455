<?php
require __DIR__ . '/init.php';

$orderId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$order = $orderId ? rcf_order($orderId) : null;

if (!$order) {
    rcf_flash('order_success', 'Unable to locate that order.');
    rcf_redirect('/admin/order.php');
}

$statusError = null;
$statusSuccess = rcf_flash('order_success');
$productIndex = [];
foreach (rcf_products() as $product) {
    $productIndex[(int)($product['id'] ?? 0)] = $product;
}

if (rcf_is_post()) {
    $token = $_POST['csrf_token'] ?? '';
    if (!rcf_verify_csrf($token)) {
        $statusError = 'Your session expired. Please try again.';
    } else {
        $newStatus = $_POST['status'] ?? $order['status'];
        $orders = rcf_orders();
        foreach ($orders as &$storedOrder) {
            if ((int)$storedOrder['id'] === $orderId) {
                $storedOrder['status'] = $newStatus;
                $storedOrder['updated_at'] = date(DATE_ATOM);
                $order = $storedOrder;
                break;
            }
        }
        unset($storedOrder);
        rcf_save_orders($orders);
        rcf_flash('order_success', 'Order status updated.');
        rcf_redirect('/admin/order-details.php?id=' . $orderId);
    }
}

$orderDate = isset($order['created_at']) ? date('F j, Y g:i A', strtotime((string)$order['created_at'])) : '—';
$statusLabel = ucfirst((string)($order['status'] ?? ''));
$items = $order['items'] ?? [];
$totalItems = 0;
$categories = [];
foreach ($items as $item) {
    $totalItems += (int)($item['quantity'] ?? 0);
    $productId = (int)($item['product_id'] ?? 0);
    if (isset($productIndex[$productId]) && !empty($productIndex[$productId]['category'])) {
        $categories[] = $productIndex[$productId]['category'];
    }
}
$categoryLabel = $categories ? implode(', ', array_unique($categories)) : '—';
$address = $order['shipping_address'] ?? [];
?>
<!DOCTYPE html>
<html lang="en">
    <!-- rts header area start -->
    <head>
        <meta charset="UTF-8">
        <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="Grocery, Store, stores">
        <title>RC Furnishing Admin</title>
        <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
        <!-- plugins css -->
        <link rel="stylesheet" href="assets/css/plugins.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css">
        <link rel="stylesheet" href="assets/css/style.css">
</head>
    <!-- rts header area end -->
<body>
<?php include __DIR__ . '/includes/admin-header.php'; ?>

            <div class="body-root-inner">
                <div class="transection">
                    <div class="title-right-actioin-btn-wrapper-product-list">
                        <h3 class="title">Order #<?php echo htmlspecialchars($order['number'], ENT_QUOTES); ?></h3>
                        <div class="button-wrapper">
                            <form action="" method="post" class="order-status-form d-flex gap-2">
                                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars(rcf_csrf_token(), ENT_QUOTES); ?>">
                                <select name="status" class="nice-select" tabindex="0">
                                    <option value="processing" <?php echo ($order['status'] ?? '') === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                    <option value="completed" <?php echo ($order['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo ($order['status'] ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    <option value="refunded" <?php echo ($order['status'] ?? '') === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                                </select>
                                <button type="submit" class="rts-btn btn-primary">Update</button>
                            </form>
                        </div>
                    </div>
                    <div class="product-top-filter-area-l">
                        <div class="left-area-button-fiulter">
                            <p>Dashboard / Orders / Order #<?php echo htmlspecialchars($order['number'], ENT_QUOTES); ?></p>
                        </div>
                    </div>
                    <?php if (!empty($statusError)): ?>
                        <div class="auth-feedback error" style="margin-top: 20px;">
                            <?php echo htmlspecialchars($statusError, ENT_QUOTES); ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($statusSuccess)): ?>
                        <div class="auth-feedback success" style="margin-top: 20px;">
                            <?php echo htmlspecialchars($statusSuccess, ENT_QUOTES); ?>
                        </div>
                    <?php endif; ?>
                    <div class="vendor-list-main-wrapper product-wrapper">
                        <!-- customers  details main wrapper -->
                         <div class="customers-details-wrapper-one-dashboard">
                            <h4 class="title">Customer Details</h4>
                            <div class="main-customers-details-top">
                                <div class="left">
                                    <img src="assets/images/avatar/03.png" alt="avatar">
                                    <div class="information-area">
                                        <h4 class="name"><?php echo htmlspecialchars($order['customer_name'], ENT_QUOTES); ?></h4>
                                        <span class="designation">Customer</span>
                                    </div>
                                </div>
                                <div class="right-area">
                                    <div class="short-contact-info">
                                        <p class="name">Email</p>
                                        <a href="mailto:<?php echo htmlspecialchars($order['customer_email'], ENT_QUOTES); ?>"><?php echo htmlspecialchars($order['customer_email'], ENT_QUOTES); ?></a>
                                    </div>
                                    <div class="short-contact-info">
                                        <p class="name">Order Date</p>
                                        <a href="#"><?php echo htmlspecialchars($orderDate, ENT_QUOTES); ?></a>
                                    </div>
                                    <div class="short-contact-info">
                                        <p class="name">Items</p>
                                        <a href="#"><?php echo $totalItems; ?></a>
                                    </div>
                                    <div class="short-contact-info">
                                        <p class="name">Status</p>
                                        <a href="#" class="status-badge" style="background:#eef2ff; color:#4338ca; text-transform: capitalize;">
                                            <?php echo htmlspecialchars($statusLabel, ENT_QUOTES); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                         </div>
                        <!-- customers  details main wrapper end -->

                        <div class="billing-address-area-4">
                            <h4 class="title">Billing Address</h4>
                            <div class="main-billing-address-wrapper">
                                <div class="single-billing-address">
                                    <p><span>Name :</span> <?php echo htmlspecialchars($order['customer_name'], ENT_QUOTES); ?></p>
                                    <p><span>Address :</span> <?php echo htmlspecialchars($address['line1'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>City :</span> <?php echo htmlspecialchars($address['city'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Region :</span> <?php echo htmlspecialchars($address['region'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Postal Code :</span> <?php echo htmlspecialchars($address['postal_code'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Country :</span> <?php echo htmlspecialchars($address['country'] ?? '', ENT_QUOTES); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="billing-address-area-4">
                            <h4 class="title">Shipping Address</h4>
                            <div class="main-billing-address-wrapper">
                                <div class="single-billing-address">
                                    <p><span>Name :</span> <?php echo htmlspecialchars($order['customer_name'], ENT_QUOTES); ?></p>
                                    <p><span>Address :</span> <?php echo htmlspecialchars($address['line1'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>City :</span> <?php echo htmlspecialchars($address['city'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Region :</span> <?php echo htmlspecialchars($address['region'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Postal Code :</span> <?php echo htmlspecialchars($address['postal_code'] ?? '', ENT_QUOTES); ?></p>
                                    <p><span>Country :</span> <?php echo htmlspecialchars($address['country'] ?? '', ENT_QUOTES); ?></p>
                                </div>
                            </div>
                        </div>
                     </div>
                </div>
                                <div class="order-details-table-1-table table-responsive">
                    <h4 class="title">Order Summary</h4>
                    <table class="table order-details-table table-responsive">
                        <thead class="bg-active">
                            <tr>
                                <th style="width: 300px;">Order Item</th>
                                <th class="text-center">Price</th>
                                <th class="text-center">Quantity</th>
                                <th class="text-right">Line Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $item): ?>
                                <?php
                                $productId = (int)($item['product_id'] ?? 0);
                                $product = $productIndex[$productId] ?? null;
                                $lineTotal = (float)($item['price'] ?? 0) * (int)($item['quantity'] ?? 0);
                                ?>
                                <tr>
                                    <td>
                                        <div class="item">
                                            <div class="thumbnail">
                                                <?php
                                                    $orderImage = trim((string)($product['image'] ?? ''));
                                                    if ($orderImage === '') {
                                                        $orderImage = 'assets/images/store/01.jpg';
                                                    }
                                                    $orderImage = rcf_admin_asset_url($orderImage);
                                                ?>
                                                <img src="<?php echo htmlspecialchars($orderImage, ENT_QUOTES); ?>" alt="item">
                                            </div>
                                            <div class="discription">
                                                <h6 class="title"><?php echo htmlspecialchars($item['name'], ENT_QUOTES); ?></h6>
                                                <span><?php echo htmlspecialchars($product['category'] ?? 'General', ENT_QUOTES); ?></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">$<?php echo number_format((float)($item['price'] ?? 0), 2); ?></td>
                                    <td class="text-center"><?php echo (int)($item['quantity'] ?? 0); ?></td>
                                    <td class="text-right">$<?php echo number_format($lineTotal, 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                            <tr class="b-n">
                                <td colspan="3" class="text-end f-w-600">Grand Total</td>
                                <td class="text-right f-w-600">$<?php echo number_format((float)$order['total'], 2); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="footer-copyright">
                    <div class="left">
                        <p>Copyright © 2024 All Right Reserved.</p>
                    </div>
                    <ul>
                        <li><a href="#">Terms</a></li>
                        <li><a href="#">Privacy</a></li>
                        <li><a href="#">Help</a></li>
                    </ul>
                </div>

            </div>

        </div>

    </div>



  <!-- rts header area start -->
  


<!-- progress area start -->
<div class="progress-wrap active-progress">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
    </svg>
</div>
<!-- progress area end -->




<div class="search-input-area">
    <div class="container">
        <div class="search-input-inner">
            <div class="input-div">
                <input class="search-input autocomplete ui-autocomplete-input" type="text" placeholder="Search by keyword or #" autocomplete="off">
                <button><i class="far fa-search"></i></button>
            </div>
        </div>
    </div>
    <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
</div>


<div id="anywhere-home"></div>

<div id="side-bar" class="side-bar header-two right-collups-add-product">
    <button class="close-icon-menu"><i class="far fa-times"></i></button>
    <!-- inner menu area desktop start -->
    <div class="right-collups-area-top">
        <h6 class="title">Add New Product</h6>
        <p>Add information and add new product</p>
    </div>
    <div class="input-main-wrapper">
        <div class="single-input">
            <label for="one">Product Name</label>
            <input type="text" id="one" placeholder="Quaker Oats Healthy Meal...">
        </div>
        <div class="single-input">
            <label for="Two">Regular Price</label>
            <input type="text" id="Two" placeholder="240">
        </div>
        <div class="single-input">
            <label for="sale">Sale Price</label>
            <input type="text" id="sale" placeholder="$250">
        </div>
        <div class="single-input">
            <label for="Stock">Stock</label>
            <input type="text" id="Stock" placeholder="530">
        </div>
        <div class="single-input">
            <label for="sku">SKU</label>
            <input type="text" id="sku" placeholder="3245">
        </div>
        <div class="single-input">
            <label for="cate">Category</label>
            <input type="text" id="cate" placeholder="Notebook">
        </div>
        <div class="single-input">
            <label for="Tag">Tag</label>
            <input type="text" id="Tag" placeholder="Iphone, Mobile">
        </div>
        <div class="single-input">
            <label for="text">Description</label>
            <textarea name="text" id="text" placeholder="Type something"></textarea>
        </div>
        <div class="single-input">
            <div class="file-upload-add-product">
                <div class="profile-left">
                    <div class="profile-image mb--30">
                        <img id="rts_image" src="assets/images/grocery/16.png" alt="Profile-NFT">
                        <span>Drag and drop Image</span>
                    </div>
                    <div class="button-area">
                        <div class="brows-file-wrapper">
                            <!-- actual upload which is hidden -->
                            <input name="rts_images1" id="rts_images1" type="file">
                            <!-- our custom upload button -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-area-botton-wrapper-p-list">
            <button class="rts-btn btn-primary">Save</button>
            <button class="rts-btn btn-primary bg-transparent">Cancel</button>
        </div>
    </div>
</div>
<?php include __DIR__ . '/includes/admin-footer.php'; ?>
