<?php
require __DIR__ . '/../app/bootstrap.php';

header('Content-Type: application/json');

$method = strtoupper($_SERVER['REQUEST_METHOD'] ?? 'GET');

function rcf_category_response($data, int $status = 200): void
{
    http_response_code($status);
    echo json_encode($data);
    exit;
}

function rcf_category_payload(): array
{
    static $cached = null;
    if ($cached !== null) {
        return $cached;
    }

    if (!empty($_POST)) {
        $cached = $_POST;
        return $cached;
    }

    $raw = file_get_contents('php://input');
    if ($raw === false || $raw === '') {
        $cached = [];
        return $cached;
    }

    $decoded = json_decode($raw, true);
    if (is_array($decoded)) {
        $cached = $decoded;
        return $cached;
    }

    parse_str($raw, $parsed);
    $cached = is_array($parsed) ? $parsed : [];
    return $cached;
}

function rcf_prepare_categories_output(array $categories, ?string $statusFilter = null): array
{
    if ($statusFilter !== null) {
        $statusFilter = strtolower(trim($statusFilter));
        $categories = array_values(array_filter($categories, static function (array $category) use ($statusFilter) {
            return strtolower((string)($category['status'] ?? 'active')) === $statusFilter;
        }));
    }

    usort($categories, static function (array $a, array $b): int {
        return strcasecmp((string)($a['name'] ?? ''), (string)($b['name'] ?? ''));
    });

    return $categories;
}

if ($method === 'GET') {
    $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
    if ($id) {
        $category = rcf_category($id);
        if (!$category) {
            rcf_category_response(['error' => 'Category not found'], 404);
        }

        rcf_category_response($category);
    }

    $statusFilter = isset($_GET['status']) ? (string)$_GET['status'] : null;
    $categories = rcf_prepare_categories_output(rcf_categories(), $statusFilter);
    rcf_category_response($categories);
}

if ($method === 'POST') {
    if (!rcf_auth_is_admin()) {
        rcf_category_response(['error' => 'Authentication required'], 401);
    }

    $file = $_FILES['categories_file'] ?? $_FILES['file'] ?? null;
    if ($file && ($file['error'] ?? UPLOAD_ERR_NO_FILE) !== UPLOAD_ERR_NO_FILE) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            rcf_category_response(['error' => 'Upload failed'], 422);
        }

        $extension = strtolower(pathinfo((string)$file['name'], PATHINFO_EXTENSION));
        $parseErrors = [];
        $rows = rcf_parse_category_upload($file['tmp_name'], $extension, $parseErrors);
        if (!empty($parseErrors)) {
            rcf_category_response(['error' => implode(' ', $parseErrors)], 422);
        }

        $summary = rcf_import_categories($rows);
        $summary['ok'] = true;

        rcf_category_response($summary);
    }

    $payload = rcf_category_payload();

    $name = trim((string)($payload['name'] ?? ''));
    $description = trim((string)($payload['description'] ?? ''));
    $status = $payload['status'] ?? null;

    $errors = [];
    $category = rcf_create_category([
        'name' => $name,
        'description' => $description,
        'status' => $status,
    ], $errors);

    if ($category === null) {
        rcf_category_response(['errors' => $errors], 422);
    }

    rcf_category_response(['ok' => true, 'category' => $category], 201);
}

if ($method === 'PUT' || $method === 'PATCH') {
    if (!rcf_auth_is_admin()) {
        rcf_category_response(['error' => 'Authentication required'], 401);
    }

    $payload = rcf_category_payload();
    $id = isset($_GET['id']) ? (int)$_GET['id'] : (int)($payload['id'] ?? 0);
    if ($id <= 0) {
        rcf_category_response(['error' => 'Category id is required'], 400);
    }

    $productsAdjusted = 0;
    $errors = [];
    $updatedCategory = rcf_update_category($id, $payload, $errors, $productsAdjusted);

    if ($updatedCategory === null) {
        $status = isset($errors['general']) ? 404 : 422;
        rcf_category_response(['errors' => $errors], $status);
    }

    rcf_category_response([
        'ok' => true,
        'category' => $updatedCategory,
        'products_adjusted' => $productsAdjusted,
    ]);
}

if ($method === 'DELETE') {
    if (!rcf_auth_is_admin()) {
        rcf_category_response(['error' => 'Authentication required'], 401);
    }

    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if ($id <= 0) {
        $payload = rcf_category_payload();
        $id = (int)($payload['id'] ?? 0);
    }

    if ($id <= 0) {
        rcf_category_response(['error' => 'Category id is required'], 400);
    }

    $productsAdjusted = 0;
    if (!rcf_delete_category($id, $productsAdjusted)) {
        rcf_category_response(['error' => 'Category not found'], 404);
    }

    rcf_category_response([
        'ok' => true,
        'products_adjusted' => $productsAdjusted,
    ]);
}

rcf_category_response(['error' => 'Method not allowed'], 405);
