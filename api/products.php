<?php
require __DIR__ . '/../app/bootstrap.php';

header('Content-Type: application/json');

$method = strtoupper($_SERVER['REQUEST_METHOD'] ?? 'GET');

if ($method === 'GET') {
    $id = isset($_GET['id']) ? (int)$_GET['id'] : null;

    if ($id) {
        $product = rcf_product($id);
        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            exit;
        }
        echo json_encode($product);
        exit;
    }

    echo json_encode(rcf_products());
    exit;
}

if ($method === 'POST') {
    if (!rcf_auth_is_admin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }

    $payload = json_decode(file_get_contents('php://input'), true) ?? [];
    $required = [
        'name' => 'Name',
        'sku' => 'SKU',
        'price' => 'Price',
        'stock' => 'Stock',
        'status' => 'Status',
    ];

    $errors = rcf_validate_required($payload, $required);

    if (!empty($errors)) {
        http_response_code(422);
        echo json_encode(['errors' => $errors]);
        exit;
    }

    $products = rcf_products();
    $payload['id'] = rcf_next_id($products);
    $payload['price'] = (float)$payload['price'];
    $payload['stock'] = (int)$payload['stock'];
    $products[] = $payload;
    rcf_save_products($products);

    echo json_encode(['ok' => true, 'product' => $payload]);
    exit;
}

http_response_code(405);
echo json_encode(['error' => 'Method not allowed']);
