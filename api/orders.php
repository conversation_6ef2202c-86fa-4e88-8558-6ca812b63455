<?php
require __DIR__ . '/../app/bootstrap.php';

header('Content-Type: application/json');

$method = strtoupper($_SERVER['REQUEST_METHOD'] ?? 'GET');

if ($method === 'GET') {
    $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
    if ($id) {
        $order = rcf_order($id);
        if (!$order) {
            http_response_code(404);
            echo json_encode(['error' => 'Order not found']);
            exit;
        }

        echo json_encode($order);
        exit;
    }

    echo json_encode(rcf_orders());
    exit;
}

if (in_array($method, ['PATCH', 'POST'], true)) {
    if (!rcf_auth_is_admin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }

    parse_str(file_get_contents('php://input'), $payload);
    $payload = array_merge($payload, $_POST);

    $id = isset($payload['id']) ? (int)$payload['id'] : null;
    if (!$id) {
        http_response_code(422);
        echo json_encode(['error' => 'Order id is required']);
        exit;
    }

    $orders = rcf_orders();
    $updated = null;
    foreach ($orders as &$order) {
        if ((int)$order['id'] === $id) {
            if (isset($payload['status'])) {
                $order['status'] = (string)$payload['status'];
            }
            $updated = $order;
            break;
        }
    }

    if (!$updated) {
        http_response_code(404);
        echo json_encode(['error' => 'Order not found']);
        exit;
    }

    rcf_save_orders($orders);
    echo json_encode(['ok' => true, 'order' => $updated]);
    exit;
}

http_response_code(405);
echo json_encode(['error' => 'Method not allowed']);
